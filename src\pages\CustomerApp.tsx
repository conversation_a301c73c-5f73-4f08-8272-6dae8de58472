import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ShoppingCart, Plus, Minus, X, MapPin, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { siteConfig } from '@/lib/config';
import { ordersAPI } from '@/lib/api';
import TypewriterAnnouncement from '@/components/TypewriterAnnouncement';
import SEO from '@/components/SEO';

// 類型定義
interface ProductVariant {
  id: number;
  name: string;
  price: number;
  stock: number;
  images: string[];
  discountRules?: DiscountRule[];
}

interface DiscountRule {
  quantity: number;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
}

interface Flavor {
  id: number;
  name: string;
  available: boolean;
}

interface CartItem {
  variant: ProductVariant;
  flavors: Flavor[];
  quantity: number;
  totalPrice: number;
}

interface OrderForm {
  name: string;
  phone: string;
  email: string;
  store711: string;
  notes: string;
}

const CustomerApp: React.FC = () => {
  const { toast } = useToast();
  
  // 狀態管理
  const [variants, setVariants] = useState<ProductVariant[]>([]);
  const [flavors, setFlavors] = useState<Flavor[]>([]);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [selectedFlavors, setSelectedFlavors] = useState<Flavor[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [orderForm, setOrderForm] = useState<OrderForm>({
    name: '',
    phone: '',
    email: '',
    store711: '',
    notes: ''
  });
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderNumber, setOrderNumber] = useState('');

  // 模擬數據 - 實際應該從 API 獲取
  useEffect(() => {
    // 模擬品相數據
    const mockVariants: ProductVariant[] = [
      {
        id: 1,
        name: '經典款',
        price: 350,
        stock: 50,
        images: ['/images/variant1-1.jpg', '/images/variant1-2.jpg'],
        discountRules: [
          { quantity: 2, discountType: 'percentage', discountValue: 10 },
          { quantity: 5, discountType: 'percentage', discountValue: 20 }
        ]
      },
      {
        id: 2,
        name: '豪華款',
        price: 450,
        stock: 30,
        images: ['/images/variant2-1.jpg', '/images/variant2-2.jpg', '/images/variant2-3.jpg'],
        discountRules: [
          { quantity: 3, discountType: 'fixed', discountValue: 100 }
        ]
      }
    ];

    // 模擬口味數據
    const mockFlavors: Flavor[] = [
      { id: 1, name: '草莓', available: true },
      { id: 2, name: '芒果', available: true },
      { id: 3, name: '薄荷', available: true },
      { id: 4, name: '香草', available: true },
      { id: 5, name: '巧克力', available: true },
      { id: 6, name: '檸檬', available: false },
      { id: 7, name: '藍莓', available: true },
      { id: 8, name: '椰子', available: true },
    ];

    setVariants(mockVariants);
    setFlavors(mockFlavors);
  }, []);

  // 計算價格（含優惠）
  const calculatePrice = (variant: ProductVariant, qty: number): number => {
    let basePrice = variant.price * qty;
    
    if (variant.discountRules) {
      // 找到適用的最大優惠
      const applicableRule = variant.discountRules
        .filter(rule => qty >= rule.quantity)
        .sort((a, b) => b.quantity - a.quantity)[0];
      
      if (applicableRule) {
        if (applicableRule.discountType === 'percentage') {
          basePrice = basePrice * (1 - applicableRule.discountValue / 100);
        } else {
          basePrice = basePrice - applicableRule.discountValue;
        }
      }
    }
    
    return Math.max(0, basePrice);
  };

  // 添加到購物車
  const addToCart = () => {
    if (!selectedVariant) {
      toast({ title: '請選擇品相', variant: 'destructive' });
      return;
    }
    
    if (selectedFlavors.length === 0) {
      toast({ title: '請至少選擇一種口味', variant: 'destructive' });
      return;
    }

    const totalPrice = calculatePrice(selectedVariant, quantity);
    const cartItem: CartItem = {
      variant: selectedVariant,
      flavors: [...selectedFlavors],
      quantity,
      totalPrice
    };

    setCart(prev => [...prev, cartItem]);
    setSelectedVariant(null);
    setSelectedFlavors([]);
    setQuantity(1);
    
    toast({ title: '已加入購物車' });
  };

  // 切換口味選擇
  const toggleFlavor = (flavor: Flavor) => {
    if (!flavor.available) return;
    
    setSelectedFlavors(prev => {
      const exists = prev.find(f => f.id === flavor.id);
      if (exists) {
        return prev.filter(f => f.id !== flavor.id);
      } else {
        return [...prev, flavor];
      }
    });
  };

  // 購物車總計
  const cartTotal = cart.reduce((sum, item) => sum + item.totalPrice, 0);

  // 提交訂單
  const submitOrder = async () => {
    if (!orderForm.name || !orderForm.phone || !orderForm.store711) {
      toast({ title: '請填寫必要資訊', variant: 'destructive' });
      return;
    }

    setIsSubmitting(true);

    try {
      // 生成訂單號
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hour = String(now.getHours()).padStart(2, '0');
      const minute = String(now.getMinutes()).padStart(2, '0');
      const generatedOrderNumber = `ORD${year}${day}${month}${hour}${minute}`;

      // 準備訂單數據
      const orderData = {
        orderNumber: generatedOrderNumber,
        customerInfo: {
          name: orderForm.name,
          phone: orderForm.phone,
          email: orderForm.email,
          storeNumber: orderForm.store711,
          storeName: orderForm.store711, // 這裡可以後續改為實際店名
        },
        items: cart.map(item => ({
          product_id: item.variant.id,
          name: item.variant.name,
          quantity: item.quantity,
          price: item.variant.price,
          total_price: item.totalPrice,
          variant_value: item.flavors.map(f => f.name).join(', '),
          variant_type: '口味'
        })),
        totals: {
          subtotal: cartTotal,
          shipping: 0, // 7-11 取貨免運費
          discount: 0,
          finalTotal: cartTotal
        },
        shippingMethod: '7-11',
        notes: orderForm.notes
      };

      const response = await ordersAPI.submitOrder(orderData);

      if (response.data.success) {
        setOrderNumber(response.data.orderNumber);
        setOrderSuccess(true);
        setShowOrderForm(false);
        setCart([]);
        setOrderForm({
          name: '',
          phone: '',
          email: '',
          store711: '',
          notes: ''
        });

        toast({
          title: '訂單提交成功',
          description: `訂單編號：${response.data.orderNumber}`
        });
      }
    } catch (error: any) {
      console.error('訂單提交失敗:', error);
      toast({
        title: '訂單提交失敗',
        description: error.response?.data?.error || error.message,
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <SEO
        title={`${siteConfig.brandName} - 產品選擇`}
        description="選擇您喜愛的產品和口味"
      />
      
      {/* 打字機公告 */}
      <div className="container mx-auto px-4 pt-4">
        <TypewriterAnnouncement />
      </div>
      
      {/* 頂部導航 */}
      <header className="bg-white shadow-sm sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-800">{siteConfig.brandName}</h1>
          <Button
            variant="outline"
            onClick={() => setShowCart(true)}
            className="relative"
          >
            <ShoppingCart className="h-5 w-5 mr-2" />
            購物車
            {cart.length > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0">
                {cart.length}
              </Badge>
            )}
          </Button>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* 品相選擇區 */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">選擇品相</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {variants.map((variant) => (
                    <div
                      key={variant.id}
                      className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                        selectedVariant?.id === variant.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedVariant(variant)}
                    >
                      {/* 圖片輪播 */}
                      <div className="relative mb-3">
                        <img
                          src={variant.images[currentImageIndex] || '/images/placeholder.jpg'}
                          alt={variant.name}
                          className="w-full h-32 object-cover rounded"
                        />
                        {variant.images.length > 1 && (
                          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                            {variant.images.map((_, index) => (
                              <button
                                key={index}
                                className={`w-2 h-2 rounded-full ${
                                  index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                                }`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setCurrentImageIndex(index);
                                }}
                              />
                            ))}
                          </div>
                        )}
                      </div>
                      
                      <h3 className="font-semibold text-lg">{variant.name}</h3>
                      <p className="text-blue-600 font-bold">NT$ {variant.price}</p>
                      <p className="text-sm text-gray-500">庫存: {variant.stock}</p>
                      
                      {/* 優惠規則 */}
                      {variant.discountRules && variant.discountRules.length > 0 && (
                        <div className="mt-2">
                          <p className="text-xs text-green-600 font-medium">優惠方案:</p>
                          {variant.discountRules.map((rule, index) => (
                            <p key={index} className="text-xs text-green-600">
                              買{rule.quantity}件{' '}
                              {rule.discountType === 'percentage' 
                                ? `打${10 - rule.discountValue/10}折` 
                                : `折${rule.discountValue}元`}
                            </p>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 口味選擇區 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">選擇口味</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {flavors.map((flavor) => (
                    <Button
                      key={flavor.id}
                      variant={selectedFlavors.find(f => f.id === flavor.id) ? "default" : "outline"}
                      disabled={!flavor.available}
                      onClick={() => toggleFlavor(flavor)}
                      className="h-12"
                    >
                      {flavor.name}
                      {!flavor.available && <span className="ml-1 text-xs">(缺貨)</span>}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右側選擇摘要 */}
          <div className="space-y-6">
            <Card className="sticky top-24">
              <CardHeader>
                <CardTitle>您的選擇</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedVariant ? (
                  <>
                    <div>
                      <h3 className="font-semibold">{selectedVariant.name}</h3>
                      <p className="text-blue-600">NT$ {selectedVariant.price}</p>
                    </div>
                    
                    {selectedFlavors.length > 0 && (
                      <div>
                        <p className="font-medium mb-2">已選口味:</p>
                        <div className="flex flex-wrap gap-1">
                          {selectedFlavors.map((flavor) => (
                            <Badge key={flavor.id} variant="secondary">
                              {flavor.name}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div className="flex items-center space-x-3">
                      <Label>數量:</Label>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="w-8 text-center">{quantity}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setQuantity(quantity + 1)}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="border-t pt-3">
                      <p className="text-lg font-bold">
                        總計: NT$ {calculatePrice(selectedVariant, quantity)}
                      </p>
                      {selectedVariant.discountRules && quantity > 1 && (
                        <p className="text-sm text-green-600">
                          已套用優惠價格
                        </p>
                      )}
                    </div>
                    
                    <Button 
                      onClick={addToCart} 
                      className="w-full"
                      disabled={selectedFlavors.length === 0}
                    >
                      加入購物車
                    </Button>
                  </>
                ) : (
                  <p className="text-gray-500 text-center py-8">
                    請選擇品相開始訂購
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 購物車對話框 */}
      <Dialog open={showCart} onOpenChange={setShowCart}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>購物車</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {cart.length === 0 ? (
              <p className="text-center text-gray-500 py-8">購物車是空的</p>
            ) : (
              <>
                {cart.map((item, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="font-semibold">{item.variant.name}</h3>
                        <p className="text-sm text-gray-600">
                          口味: {item.flavors.map(f => f.name).join(', ')}
                        </p>
                        <p className="text-sm">數量: {item.quantity}</p>
                        <p className="font-bold text-blue-600">NT$ {item.totalPrice}</p>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setCart(prev => prev.filter((_, i) => i !== index))}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
                
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center text-lg font-bold">
                    <span>總計:</span>
                    <span>NT$ {cartTotal}</span>
                  </div>
                  <Button 
                    onClick={() => {
                      setShowCart(false);
                      setShowOrderForm(true);
                    }}
                    className="w-full mt-4"
                    disabled={cart.length === 0}
                  >
                    前往結帳
                  </Button>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* 訂單表單對話框 */}
      <Dialog open={showOrderForm} onOpenChange={setShowOrderForm}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>訂購資訊</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">姓名 *</Label>
              <Input
                id="name"
                value={orderForm.name}
                onChange={(e) => setOrderForm(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="phone">聯絡電話 *</Label>
              <Input
                id="phone"
                value={orderForm.phone}
                onChange={(e) => setOrderForm(prev => ({ ...prev, phone: e.target.value }))}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="email">電子郵件</Label>
              <Input
                id="email"
                type="email"
                value={orderForm.email}
                onChange={(e) => setOrderForm(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="store711">7-11 取貨門市 *</Label>
              <div className="flex space-x-2">
                <Input
                  id="store711"
                  value={orderForm.store711}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, store711: e.target.value }))}
                  placeholder="請輸入門市代號或名稱"
                  required
                />
                <Button variant="outline" size="sm">
                  <MapPin className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div>
              <Label htmlFor="notes">備註</Label>
              <Textarea
                id="notes"
                value={orderForm.notes}
                onChange={(e) => setOrderForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="特殊需求或備註事項"
              />
            </div>
            
            <div className="border-t pt-4">
              <div className="flex justify-between items-center mb-4">
                <span className="font-semibold">總金額:</span>
                <span className="text-xl font-bold text-blue-600">NT$ {cartTotal}</span>
              </div>
              <p className="text-sm text-gray-600 mb-4">付款方式: 7-11 取貨付款</p>
              
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  onClick={() => setShowOrderForm(false)}
                  className="flex-1"
                >
                  取消
                </Button>
                <Button
                  onClick={submitOrder}
                  className="flex-1"
                  disabled={!orderForm.name || !orderForm.phone || !orderForm.store711 || isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      提交中...
                    </>
                  ) : (
                    '確認訂購'
                  )}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 訂單成功對話框 */}
      <Dialog open={orderSuccess} onOpenChange={setOrderSuccess}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-green-600">訂購成功！</DialogTitle>
          </DialogHeader>
          <div className="text-center py-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="text-gray-600 mb-4">
              感謝您的訂購，我們將盡快為您準備商品
            </p>
            <div className="bg-gray-50 p-4 rounded-lg mb-4">
              <p className="text-sm text-gray-500">訂單編號</p>
              <p className="font-mono font-bold text-lg">{orderNumber}</p>
            </div>
            <p className="text-sm text-gray-500 mb-6">
              請保存此訂單編號，我們會透過 Telegram 發送訂單確認通知
            </p>
            <Button
              onClick={() => setOrderSuccess(false)}
              className="w-full"
            >
              繼續購物
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CustomerApp;
