const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 創建數據庫連接
const dbPath = path.join(__dirname, '../../database/vape_store.db');
const db = new sqlite3.Database(dbPath);

// 添加多件優惠表
db.serialize(() => {
  console.log('🔄 添加多件優惠表...');
  
  // 產品變體多件優惠表
  db.run(`
    CREATE TABLE IF NOT EXISTS variant_bulk_discounts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      variant_id INTEGER NOT NULL,
      min_quantity INTEGER NOT NULL,
      discount_type TEXT NOT NULL CHECK(discount_type IN ('percentage', 'fixed')),
      discount_value DECIMAL(10,2) NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (variant_id) REFERENCES product_variants (id) ON DELETE CASCADE
    )
  `, (err) => {
    if (err) {
      console.error('❌ 創建多件優惠表失敗:', err);
    } else {
      console.log('✅ 多件優惠表創建成功！');
    }
  });

  // 產品圖片表
  db.run(`
    CREATE TABLE IF NOT EXISTS product_images (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      product_id INTEGER NOT NULL,
      image_url TEXT NOT NULL,
      sort_order INTEGER DEFAULT 0,
      is_primary INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
    )
  `, (err) => {
    if (err) {
      console.error('❌ 創建產品圖片表失敗:', err);
    } else {
      console.log('✅ 產品圖片表創建成功！');
    }
  });

  // 產品變體圖片表
  db.run(`
    CREATE TABLE IF NOT EXISTS variant_images (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      variant_id INTEGER NOT NULL,
      image_url TEXT NOT NULL,
      sort_order INTEGER DEFAULT 0,
      is_primary INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (variant_id) REFERENCES product_variants (id) ON DELETE CASCADE
    )
  `, (err) => {
    if (err) {
      console.error('❌ 創建變體圖片表失敗:', err);
    } else {
      console.log('✅ 變體圖片表創建成功！');
    }
  });

  console.log('✅ 所有新表創建完成！');
});

db.close((err) => {
  if (err) {
    console.error('❌ 數據庫關閉失敗:', err.message);
  } else {
    console.log('✅ 數據庫連接已關閉');
  }
});
