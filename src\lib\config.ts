// 網站配置
export const siteConfig = {
  // 網站基本信息
  name: import.meta.env.VITE_SITE_NAME || 'Your Store',
  title: import.meta.env.VITE_SITE_TITLE || 'Your Store - Professional Online Shopping Platform',
  description: import.meta.env.VITE_SITE_DESCRIPTION || 'Your store description here',

  // 域名配置
  domain: import.meta.env.VITE_SITE_URL || 'https://your-domain.com',

  // SEO配置
  keywords: import.meta.env.VITE_SITE_KEYWORDS || 'ecommerce,online store,shopping',
  author: import.meta.env.VITE_BRAND_NAME || 'Your Brand',

  // 品牌配置
  brandName: import.meta.env.VITE_BRAND_NAME || 'Your Brand',
  brandSlogan: import.meta.env.VITE_BRAND_SLOGAN || 'Your Brand Slogan',
  brandDescription: import.meta.env.VITE_BRAND_DESCRIPTION || 'Your brand description',

  // 社交媒體
  social: {
    line: import.meta.env.VITE_SOCIAL_LINE || '@your-line-id',
    email: import.meta.env.VITE_SOCIAL_EMAIL || '<EMAIL>'
  },

  // 圖片配置
  defaultImage: import.meta.env.VITE_DEFAULT_IMAGE || '/images/default-image.jpg',
  logo: import.meta.env.VITE_LOGO_IMAGE || '/logo.svg',
  heroImage: import.meta.env.VITE_HERO_IMAGE || '/images/hero-image.jpg',

  // 聯繫信息
  contact: {
    line: import.meta.env.VITE_CONTACT_LINE || '@your-line-id',
    email: import.meta.env.VITE_CONTACT_EMAIL || '<EMAIL>',
    supportEmail: import.meta.env.VITE_CONTACT_SUPPORT_EMAIL || '<EMAIL>',
    hours: import.meta.env.VITE_CONTACT_HOURS || '週一至週五 9:00-18:00'
  },

  // 產品分類配置
  categories: {
    host: import.meta.env.VITE_CATEGORY_HOST_NAME || '主要產品',
    cartridge: import.meta.env.VITE_CATEGORY_CARTRIDGE_NAME || '配件產品',
    disposable: import.meta.env.VITE_CATEGORY_DISPOSABLE_NAME || '特殊產品'
  },

  // 服務特色
  features: {
    delivery: {
      title: import.meta.env.VITE_FEATURE_DELIVERY_TITLE || '快速配送',
      description: import.meta.env.VITE_FEATURE_DELIVERY_DESC || '快速配送服務'
    },
    quality: {
      title: import.meta.env.VITE_FEATURE_QUALITY_TITLE || '品質保證',
      description: import.meta.env.VITE_FEATURE_QUALITY_DESC || '品質保證服務'
    },
    shipping: {
      title: import.meta.env.VITE_FEATURE_SHIPPING_TITLE || '免費配送',
      description: import.meta.env.VITE_FEATURE_SHIPPING_DESC || '免費配送服務'
    },
    service: {
      title: import.meta.env.VITE_FEATURE_SERVICE_TITLE || '售後服務',
      description: import.meta.env.VITE_FEATURE_SERVICE_DESC || '售後服務'
    }
  },

  // 版權信息
  copyright: {
    year: import.meta.env.VITE_COPYRIGHT_YEAR || '2025',
    text: import.meta.env.VITE_COPYRIGHT_TEXT || '版權所有'
  },

  // 年齡驗證
  ageVerification: {
    title: import.meta.env.VITE_AGE_VERIFICATION_TITLE || '年齡驗證',
    message: import.meta.env.VITE_AGE_VERIFICATION_MESSAGE || '本網站商品僅供成年人使用',
    warning: import.meta.env.VITE_AGE_WARNING || '重要提醒：請負責任地使用我們的產品。'
  }
};

// 獲取完整URL
export const getFullUrl = (path: string = '') => {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${siteConfig.domain}${cleanPath}`;
};

// 獲取完整圖片URL
export const getFullImageUrl = (imagePath: string) => {
  if (imagePath.startsWith('http')) {
    return imagePath;
  }
  return getFullUrl(imagePath);
};
