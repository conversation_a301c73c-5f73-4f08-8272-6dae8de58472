// 網站配置
export const siteConfig = {
  // 網站基本信息
  name: import.meta.env.VITE_SITE_NAME || 'Your Store',
  title: import.meta.env.VITE_SITE_TITLE || 'Your Store - Professional Online Shopping Platform',
  description: import.meta.env.VITE_SITE_DESCRIPTION || 'Your store description here',

  // 域名配置
  domain: import.meta.env.VITE_SITE_URL || 'https://your-domain.com',

  // SEO配置
  keywords: import.meta.env.VITE_SITE_KEYWORDS || 'ecommerce,online store,shopping',
  author: import.meta.env.VITE_BRAND_NAME || 'Your Brand',

  // 品牌配置
  brandName: import.meta.env.VITE_BRAND_NAME || 'Your Brand',
  brandSlogan: import.meta.env.VITE_BRAND_SLOGAN || 'Your Brand Slogan',
  brandDescription: import.meta.env.VITE_BRAND_DESCRIPTION || 'Your brand description',

  // 社交媒體
  social: {
    line: import.meta.env.VITE_SOCIAL_LINE || '@your-line-id',
    email: import.meta.env.VITE_SOCIAL_EMAIL || '<EMAIL>'
  },

  // 圖片配置
  defaultImage: import.meta.env.VITE_DEFAULT_IMAGE || '/images/default-image.jpg',
  logo: import.meta.env.VITE_LOGO_IMAGE || '/logo.svg',
  heroImage: import.meta.env.VITE_HERO_IMAGE || '/images/hero-image.jpg',

  // 聯繫信息
  contact: {
    line: import.meta.env.VITE_CONTACT_LINE || '@your-line-id',
    email: import.meta.env.VITE_CONTACT_EMAIL || '<EMAIL>',
    supportEmail: import.meta.env.VITE_CONTACT_SUPPORT_EMAIL || '<EMAIL>',
    hours: import.meta.env.VITE_CONTACT_HOURS || '週一至週五 9:00-18:00'
  },

  // 產品分類配置
  categories: {
    host: import.meta.env.VITE_CATEGORY_HOST_NAME || '主要產品',
    cartridge: import.meta.env.VITE_CATEGORY_CARTRIDGE_NAME || '配件產品',
    disposable: import.meta.env.VITE_CATEGORY_DISPOSABLE_NAME || '特殊產品'
  },

  // 服務特色
  features: {
    delivery: {
      title: import.meta.env.VITE_FEATURE_DELIVERY_TITLE || '快速配送',
      description: import.meta.env.VITE_FEATURE_DELIVERY_DESC || '快速配送服務'
    },
    quality: {
      title: import.meta.env.VITE_FEATURE_QUALITY_TITLE || '品質保證',
      description: import.meta.env.VITE_FEATURE_QUALITY_DESC || '品質保證服務'
    },
    shipping: {
      title: import.meta.env.VITE_FEATURE_SHIPPING_TITLE || '免費配送',
      description: import.meta.env.VITE_FEATURE_SHIPPING_DESC || '免費配送服務'
    },
    service: {
      title: import.meta.env.VITE_FEATURE_SERVICE_TITLE || '售後服務',
      description: import.meta.env.VITE_FEATURE_SERVICE_DESC || '售後服務'
    }
  },

  // 版權信息
  copyright: {
    year: import.meta.env.VITE_COPYRIGHT_YEAR || '2025',
    text: import.meta.env.VITE_COPYRIGHT_TEXT || '版權所有'
  },

  // 年齡驗證
  ageVerification: {
    title: import.meta.env.VITE_AGE_VERIFICATION_TITLE || '年齡驗證',
    message: import.meta.env.VITE_AGE_VERIFICATION_MESSAGE || '本網站商品僅供成年人使用',
    warning: import.meta.env.VITE_AGE_WARNING || '重要提醒：請負責任地使用我們的產品。'
  },

  // 界面文字配置
  ui: {
    // 導航和按鈕
    cart: import.meta.env.VITE_UI_CART_BUTTON || '購物車',
    addToCart: import.meta.env.VITE_UI_ADD_TO_CART || '加入購物車',
    checkout: import.meta.env.VITE_UI_CHECKOUT || '立即結帳',
    confirmOrder: import.meta.env.VITE_UI_CONFIRM_ORDER || '確認訂購',
    cancel: import.meta.env.VITE_UI_CANCEL || '取消',
    close: import.meta.env.VITE_UI_CLOSE || '關閉',
    continueShopping: import.meta.env.VITE_UI_CONTINUE_SHOPPING || '繼續購物',
    remove: import.meta.env.VITE_UI_REMOVE || '移除',
    quantity: import.meta.env.VITE_UI_QUANTITY || '數量',
    total: import.meta.env.VITE_UI_TOTAL || '總計',
    subtotal: import.meta.env.VITE_UI_SUBTOTAL || '小計',
    shipping: import.meta.env.VITE_UI_SHIPPING || '運費',
    discount: import.meta.env.VITE_UI_DISCOUNT || '折扣',

    // 表單標籤
    form: {
      name: import.meta.env.VITE_UI_FORM_NAME || '姓名',
      phone: import.meta.env.VITE_UI_FORM_PHONE || '聯絡電話',
      email: import.meta.env.VITE_UI_FORM_EMAIL || '電子郵件',
      store: import.meta.env.VITE_UI_FORM_STORE || '7-11 取貨門市',
      notes: import.meta.env.VITE_UI_FORM_NOTES || '備註',
      required: import.meta.env.VITE_UI_FORM_REQUIRED || '必填',
      optional: import.meta.env.VITE_UI_FORM_OPTIONAL || '選填',
      placeholders: {
        name: import.meta.env.VITE_UI_FORM_PLACEHOLDER_NAME || '請輸入您的姓名',
        phone: import.meta.env.VITE_UI_FORM_PLACEHOLDER_PHONE || '請輸入聯絡電話',
        email: import.meta.env.VITE_UI_FORM_PLACEHOLDER_EMAIL || '請輸入電子郵件',
        store: import.meta.env.VITE_UI_FORM_PLACEHOLDER_STORE || '請選擇取貨門市',
        notes: import.meta.env.VITE_UI_FORM_PLACEHOLDER_NOTES || '特殊需求或備註事項'
      }
    },

    // 訊息和狀態
    messages: {
      successTitle: import.meta.env.VITE_UI_SUCCESS_TITLE || '訂購成功！',
      successMessage: import.meta.env.VITE_UI_SUCCESS_MESSAGE || '感謝您的訂購，我們將盡快為您準備商品',
      orderNumber: import.meta.env.VITE_UI_SUCCESS_ORDER_NUMBER || '訂單編號',
      errorTitle: import.meta.env.VITE_UI_ERROR_TITLE || '發生錯誤',
      errorMessage: import.meta.env.VITE_UI_ERROR_MESSAGE || '請稍後再試或聯繫客服',
      loading: import.meta.env.VITE_UI_LOADING || '載入中...',
      emptyCart: import.meta.env.VITE_UI_EMPTY_CART || '購物車是空的',
      selectProduct: import.meta.env.VITE_UI_SELECT_PRODUCT || '請選擇商品',
      selectFlavor: import.meta.env.VITE_UI_SELECT_FLAVOR || '請選擇口味',
      outOfStock: import.meta.env.VITE_UI_OUT_OF_STOCK || '缺貨',
      inStock: import.meta.env.VITE_UI_IN_STOCK || '有庫存'
    },

    // 產品相關
    product: {
      selection: import.meta.env.VITE_UI_PRODUCT_SELECTION || '商品選擇',
      flavorSelection: import.meta.env.VITE_UI_FLAVOR_SELECTION || '口味選擇',
      toppingSelection: import.meta.env.VITE_UI_TOPPING_SELECTION || '配料選擇',
      price: import.meta.env.VITE_UI_PRICE || '價格',
      stock: import.meta.env.VITE_UI_STOCK || '庫存',
      description: import.meta.env.VITE_UI_DESCRIPTION || '商品描述',
      category: import.meta.env.VITE_UI_CATEGORY || '分類',
      allCategories: import.meta.env.VITE_UI_ALL_CATEGORIES || '全部分類'
    },

    // 訂單相關
    order: {
      info: import.meta.env.VITE_UI_ORDER_INFO || '訂購資訊',
      summary: import.meta.env.VITE_UI_ORDER_SUMMARY || '訂單摘要',
      details: import.meta.env.VITE_UI_ORDER_DETAILS || '訂單詳情',
      status: import.meta.env.VITE_UI_ORDER_STATUS || '訂單狀態',
      date: import.meta.env.VITE_UI_ORDER_DATE || '訂購日期',
      deliveryInfo: import.meta.env.VITE_UI_DELIVERY_INFO || '配送資訊',
      paymentMethod: import.meta.env.VITE_UI_PAYMENT_METHOD || '付款方式',
      paymentCOD: import.meta.env.VITE_UI_PAYMENT_COD || '貨到付款'
    },

    // 通知和提示
    notifications: {
      added: import.meta.env.VITE_UI_NOTIFICATION_ADDED || '已加入購物車',
      removed: import.meta.env.VITE_UI_NOTIFICATION_REMOVED || '已從購物車移除',
      updated: import.meta.env.VITE_UI_NOTIFICATION_UPDATED || '已更新數量',
      error: import.meta.env.VITE_UI_NOTIFICATION_ERROR || '操作失敗',
      confirmRemove: import.meta.env.VITE_UI_CONFIRM_REMOVE || '確定要移除此商品嗎？',
      confirmClearCart: import.meta.env.VITE_UI_CONFIRM_CLEAR_CART || '確定要清空購物車嗎？'
    },

    // 驗證訊息
    validation: {
      required: import.meta.env.VITE_UI_VALIDATION_REQUIRED || '此欄位為必填',
      email: import.meta.env.VITE_UI_VALIDATION_EMAIL || '請輸入有效的電子郵件',
      phone: import.meta.env.VITE_UI_VALIDATION_PHONE || '請輸入有效的電話號碼',
      minLength: import.meta.env.VITE_UI_VALIDATION_MIN_LENGTH || '至少需要 {min} 個字符',
      maxLength: import.meta.env.VITE_UI_VALIDATION_MAX_LENGTH || '最多 {max} 個字符'
    }
  }
};

// 獲取完整URL
export const getFullUrl = (path: string = '') => {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${siteConfig.domain}${cleanPath}`;
};

// 獲取完整圖片URL
export const getFullImageUrl = (imagePath: string) => {
  if (imagePath.startsWith('http')) {
    return imagePath;
  }
  return getFullUrl(imagePath);
};
