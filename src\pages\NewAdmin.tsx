import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from "@/hooks/use-toast";
import {
  Package,
  ShoppingCart,
  Gift,
  MessageSquare,
  Users,
  Settings,
  Eye,
  EyeOff,
  Loader2,
  LogOut,
  Plus,
  Edit,
  Trash2,
  Search
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// 類型定義
interface LoginForm {
  username: string;
  password: string;
}

interface DashboardStats {
  totalProducts: number;
  totalOrders: number;
  totalCoupons: number;
  totalAnnouncements: number;
}

interface Product {
  id: number;
  name: string;
  category: string;
  brand: string;
  price: number;
  description: string;
  image_url: string;
  stock: number;
  created_at: string;
}

interface ProductForm {
  name: string;
  category: string;
  brand: string;
  price: string;
  description: string;
  image_url: string;
  stock: string;
}

interface Order {
  id: number;
  order_number: string;
  customer_name: string;
  customer_phone: string;
  customer_email: string;
  total_amount: number;
  status: string;
  created_at: string;
  items?: OrderItem[];
}

interface OrderItem {
  id: number;
  product_name: string;
  quantity: number;
  price: number;
  flavor?: string;
}

const NewAdminPage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // 狀態管理
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [loginForm, setLoginForm] = useState<LoginForm>({ username: '', password: '' });
  const [showPassword, setShowPassword] = useState(false);
  const [loginError, setLoginError] = useState('');
  const [activeTab, setActiveTab] = useState('dashboard');
  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalOrders: 0,
    totalCoupons: 0,
    totalAnnouncements: 0
  });

  // 產品管理狀態
  const [products, setProducts] = useState<Product[]>([]);
  const [productForm, setProductForm] = useState<ProductForm>({
    name: '',
    category: '',
    brand: '',
    price: '',
    description: '',
    image_url: '',
    stock: ''
  });
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [showProductDialog, setShowProductDialog] = useState(false);
  const [productSearch, setProductSearch] = useState('');

  // 訂單管理狀態
  const [orders, setOrders] = useState<Order[]>([]);
  const [orderSearch, setOrderSearch] = useState('');
  const [orderStatusFilter, setOrderStatusFilter] = useState('all');

  // 檢查登入狀態
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('admin_token');
      if (token) {
        try {
          const response = await fetch('/api/admin/verify', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          if (response.ok) {
            setIsAuthenticated(true);
            await loadDashboardData();
            await loadProducts();
            await loadOrders();
          } else {
            localStorage.removeItem('admin_token');
          }
        } catch (error) {
          console.error('驗證失敗:', error);
          localStorage.removeItem('admin_token');
        }
      }
      setLoading(false);
    };

    checkAuth();
  }, []);

  // 載入儀表板數據
  const loadDashboardData = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch('/api/admin/dashboard-stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDashboardStats(data);
      }
    } catch (error) {
      console.error('載入儀表板數據失敗:', error);
    }
  };

  // 載入產品列表
  const loadProducts = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch('/api/admin/products', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProducts(data.products || []);
      }
    } catch (error) {
      console.error('載入產品列表失敗:', error);
    }
  };

  // 載入訂單列表
  const loadOrders = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch('/api/orders/admin/list', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setOrders(data.orders || []);
      }
    } catch (error) {
      console.error('載入訂單列表失敗:', error);
    }
  };

  // 登入處理
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setLoginError('');

    try {
      const response = await fetch('/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(loginForm)
      });

      const data = await response.json();

      if (response.ok) {
        localStorage.setItem('admin_token', data.token);
        setIsAuthenticated(true);
        await loadDashboardData();
        await loadProducts();
        await loadOrders();
        toast({
          title: "登入成功",
          description: "歡迎回到管理後台"
        });
      } else {
        setLoginError(data.error || '登入失敗');
      }
    } catch (error) {
      console.error('登入錯誤:', error);
      setLoginError('網路錯誤，請稍後再試');
    } finally {
      setLoading(false);
    }
  };

  // 登出處理
  const handleLogout = () => {
    localStorage.removeItem('admin_token');
    setIsAuthenticated(false);
    setLoginForm({ username: '', password: '' });
    navigate('/');
    toast({
      title: "已登出",
      description: "您已成功登出管理後台"
    });
  };

  // 產品表單處理
  const handleProductSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const token = localStorage.getItem('admin_token');
      const productData = {
        ...productForm,
        price: parseFloat(productForm.price),
        stock: parseInt(productForm.stock)
      };

      const url = editingProduct
        ? `/api/admin/products/${editingProduct.id}`
        : '/api/admin/products';

      const method = editingProduct ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(productData)
      });

      if (response.ok) {
        toast({
          title: editingProduct ? "產品更新成功" : "產品創建成功",
          description: `${productForm.name} 已${editingProduct ? '更新' : '創建'}`
        });

        setShowProductDialog(false);
        setEditingProduct(null);
        setProductForm({
          name: '',
          category: '',
          brand: '',
          price: '',
          description: '',
          image_url: '',
          stock: ''
        });
        await loadProducts();
        await loadDashboardData();
      } else {
        const data = await response.json();
        toast({
          title: "操作失敗",
          description: data.error || '請稍後再試',
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('產品操作失敗:', error);
      toast({
        title: "網路錯誤",
        description: "請檢查網路連接",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // 刪除產品
  const handleDeleteProduct = async (product: Product) => {
    if (!confirm(`確定要刪除產品 "${product.name}" 嗎？`)) return;

    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch(`/api/admin/products/${product.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        toast({
          title: "產品已刪除",
          description: `${product.name} 已成功刪除`
        });
        await loadProducts();
        await loadDashboardData();
      } else {
        const data = await response.json();
        toast({
          title: "刪除失敗",
          description: data.error || '請稍後再試',
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('刪除產品失敗:', error);
      toast({
        title: "網路錯誤",
        description: "請檢查網路連接",
        variant: "destructive"
      });
    }
  };

  // 編輯產品
  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setProductForm({
      name: product.name,
      category: product.category,
      brand: product.brand,
      price: product.price.toString(),
      description: product.description,
      image_url: product.image_url,
      stock: product.stock.toString()
    });
    setShowProductDialog(true);
  };

  // 更新訂單狀態
  const handleUpdateOrderStatus = async (orderId: number, newStatus: string) => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch(`/api/orders/admin/${orderId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (response.ok) {
        toast({
          title: "訂單狀態已更新",
          description: `訂單狀態已更新為 ${getStatusText(newStatus)}`
        });
        await loadOrders();
      } else {
        const data = await response.json();
        toast({
          title: "更新失敗",
          description: data.error || '請稍後再試',
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('更新訂單狀態失敗:', error);
      toast({
        title: "網路錯誤",
        description: "請檢查網路連接",
        variant: "destructive"
      });
    }
  };

  // 獲取狀態文字
  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      'pending': '待處理',
      'confirmed': '已確認',
      'shipped': '已出貨',
      'delivered': '已送達',
      'cancelled': '已取消'
    };
    return statusMap[status] || status;
  };

  // 獲取狀態顏色
  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'pending': 'secondary',
      'confirmed': 'default',
      'shipped': 'outline',
      'delivered': 'default',
      'cancelled': 'destructive'
    };
    return colorMap[status] || 'secondary';
  };

  // 載入中畫面
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-12 w-12 animate-spin" />
      </div>
    );
  }

  // 登入表單
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-2xl text-center">管理員登入</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">用戶名</Label>
                <Input
                  id="username"
                  type="text"
                  value={loginForm.username}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, username: e.target.value }))}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">密碼</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={loginForm.password}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {loginError && (
                <p className="text-sm text-red-500">{loginError}</p>
              )}

              <Button type="submit" className="w-full" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                登入
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 主管理介面
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 頂部導航 */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              管理後台
            </h1>
            <Button variant="outline" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              登出
            </Button>
          </div>
        </div>
      </header>

      {/* 主要內容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="dashboard">儀表板</TabsTrigger>
            <TabsTrigger value="products">產品管理</TabsTrigger>
            <TabsTrigger value="orders">訂單管理</TabsTrigger>
            <TabsTrigger value="coupons">優惠券</TabsTrigger>
            <TabsTrigger value="announcements">公告</TabsTrigger>
            <TabsTrigger value="settings">設置</TabsTrigger>
          </TabsList>

          {/* 儀表板 */}
          <TabsContent value="dashboard" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">總產品數</CardTitle>
                  <Package className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{dashboardStats.totalProducts}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">總訂單數</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{dashboardStats.totalOrders}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">優惠券數</CardTitle>
                  <Gift className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{dashboardStats.totalCoupons}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">公告數</CardTitle>
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{dashboardStats.totalAnnouncements}</div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 產品管理 */}
          <TabsContent value="products" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>產品管理</CardTitle>
                  <Dialog open={showProductDialog} onOpenChange={setShowProductDialog}>
                    <DialogTrigger asChild>
                      <Button onClick={() => {
                        setEditingProduct(null);
                        setProductForm({
                          name: '',
                          category: '',
                          brand: '',
                          price: '',
                          description: '',
                          image_url: '',
                          stock: ''
                        });
                      }}>
                        <Plus className="h-4 w-4 mr-2" />
                        新增產品
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>
                          {editingProduct ? '編輯產品' : '新增產品'}
                        </DialogTitle>
                      </DialogHeader>
                      <form onSubmit={handleProductSubmit} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="name">產品名稱</Label>
                            <Input
                              id="name"
                              value={productForm.name}
                              onChange={(e) => setProductForm(prev => ({ ...prev, name: e.target.value }))}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="category">類別</Label>
                            <Select value={productForm.category} onValueChange={(value) => setProductForm(prev => ({ ...prev, category: value }))}>
                              <SelectTrigger>
                                <SelectValue placeholder="選擇類別" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="host">主機</SelectItem>
                                <SelectItem value="cartridge">煙彈</SelectItem>
                                <SelectItem value="disposable">拋棄式</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="brand">品牌</Label>
                            <Input
                              id="brand"
                              value={productForm.brand}
                              onChange={(e) => setProductForm(prev => ({ ...prev, brand: e.target.value }))}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="price">價格</Label>
                            <Input
                              id="price"
                              type="number"
                              step="0.01"
                              value={productForm.price}
                              onChange={(e) => setProductForm(prev => ({ ...prev, price: e.target.value }))}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="stock">庫存</Label>
                            <Input
                              id="stock"
                              type="number"
                              value={productForm.stock}
                              onChange={(e) => setProductForm(prev => ({ ...prev, stock: e.target.value }))}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="image_url">圖片網址</Label>
                            <Input
                              id="image_url"
                              value={productForm.image_url}
                              onChange={(e) => setProductForm(prev => ({ ...prev, image_url: e.target.value }))}
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="description">產品描述</Label>
                          <Textarea
                            id="description"
                            value={productForm.description}
                            onChange={(e) => setProductForm(prev => ({ ...prev, description: e.target.value }))}
                            rows={3}
                          />
                        </div>
                        <DialogFooter>
                          <Button type="button" variant="outline" onClick={() => setShowProductDialog(false)}>
                            取消
                          </Button>
                          <Button type="submit" disabled={loading}>
                            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                            {editingProduct ? '更新' : '創建'}
                          </Button>
                        </DialogFooter>
                      </form>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Search className="h-4 w-4" />
                    <Input
                      placeholder="搜尋產品..."
                      value={productSearch}
                      onChange={(e) => setProductSearch(e.target.value)}
                      className="max-w-sm"
                    />
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>產品名稱</TableHead>
                        <TableHead>類別</TableHead>
                        <TableHead>品牌</TableHead>
                        <TableHead>價格</TableHead>
                        <TableHead>庫存</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {products
                        .filter(product =>
                          product.name.toLowerCase().includes(productSearch.toLowerCase()) ||
                          product.brand.toLowerCase().includes(productSearch.toLowerCase())
                        )
                        .map((product) => (
                          <TableRow key={product.id}>
                            <TableCell className="font-medium">{product.name}</TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {product.category === 'host' ? '主機' :
                                 product.category === 'cartridge' ? '煙彈' : '拋棄式'}
                              </Badge>
                            </TableCell>
                            <TableCell>{product.brand}</TableCell>
                            <TableCell>NT${product.price}</TableCell>
                            <TableCell>
                              <Badge variant={product.stock > 10 ? "default" : product.stock > 0 ? "secondary" : "destructive"}>
                                {product.stock}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleEditProduct(product)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDeleteProduct(product)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="orders" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>訂單管理</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Search className="h-4 w-4" />
                      <Input
                        placeholder="搜尋訂單..."
                        value={orderSearch}
                        onChange={(e) => setOrderSearch(e.target.value)}
                        className="max-w-sm"
                      />
                    </div>
                    <Select value={orderStatusFilter} onValueChange={setOrderStatusFilter}>
                      <SelectTrigger className="w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">所有狀態</SelectItem>
                        <SelectItem value="pending">待處理</SelectItem>
                        <SelectItem value="confirmed">已確認</SelectItem>
                        <SelectItem value="shipped">已出貨</SelectItem>
                        <SelectItem value="delivered">已送達</SelectItem>
                        <SelectItem value="cancelled">已取消</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>訂單編號</TableHead>
                        <TableHead>客戶姓名</TableHead>
                        <TableHead>聯絡電話</TableHead>
                        <TableHead>總金額</TableHead>
                        <TableHead>狀態</TableHead>
                        <TableHead>建立時間</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {orders
                        .filter(order => {
                          const matchesSearch = order.order_number.toLowerCase().includes(orderSearch.toLowerCase()) ||
                                              order.customer_name.toLowerCase().includes(orderSearch.toLowerCase()) ||
                                              order.customer_phone.includes(orderSearch);
                          const matchesStatus = orderStatusFilter === 'all' || order.status === orderStatusFilter;
                          return matchesSearch && matchesStatus;
                        })
                        .map((order) => (
                          <TableRow key={order.id}>
                            <TableCell className="font-medium">{order.order_number}</TableCell>
                            <TableCell>{order.customer_name}</TableCell>
                            <TableCell>{order.customer_phone}</TableCell>
                            <TableCell>NT${order.total_amount}</TableCell>
                            <TableCell>
                              <Badge variant={getStatusColor(order.status) as any}>
                                {getStatusText(order.status)}
                              </Badge>
                            </TableCell>
                            <TableCell>{new Date(order.created_at).toLocaleDateString('zh-TW')}</TableCell>
                            <TableCell>
                              <Select
                                value={order.status}
                                onValueChange={(value) => handleUpdateOrderStatus(order.id, value)}
                              >
                                <SelectTrigger className="w-32">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="pending">待處理</SelectItem>
                                  <SelectItem value="confirmed">已確認</SelectItem>
                                  <SelectItem value="shipped">已出貨</SelectItem>
                                  <SelectItem value="delivered">已送達</SelectItem>
                                  <SelectItem value="cancelled">已取消</SelectItem>
                                </SelectContent>
                              </Select>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="coupons">
            <Card>
              <CardHeader>
                <CardTitle>優惠券管理</CardTitle>
              </CardHeader>
              <CardContent>
                <p>優惠券管理功能開發中...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="announcements">
            <Card>
              <CardHeader>
                <CardTitle>公告管理</CardTitle>
              </CardHeader>
              <CardContent>
                <p>公告管理功能開發中...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>系統設置</CardTitle>
              </CardHeader>
              <CardContent>
                <p>系統設置功能開發中...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default NewAdminPage;
