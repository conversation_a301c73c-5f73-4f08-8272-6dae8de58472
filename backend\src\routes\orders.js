const express = require('express');
const { dbAsync } = require('../database/db');
const ExcelJS = require('exceljs');
const router = express.Router();

// 提交訂單並發送Telegram通知
router.post('/submit', async (req, res) => {
  const { orderData } = req.body;
  if (!orderData) {
    return res.status(400).json({ error: '缺少訂單數據' });
  }

  // 為 orderData 添加 orderNumber 佔位符，以便通知函數使用
  // 實際的 orderNumber 會在稍後生成並更新
  const tempOrderNumber = `ORD-TEMP-${Date.now()}`;
  const orderDataForNotification = { ...orderData, orderNumber: tempOrderNumber };


  try {
    await dbAsync.run('BEGIN TRANSACTION');

    // 1. 插入訂單主表，讓數據庫生成ID
    const orderResult = await dbAsync.run(`
      INSERT INTO orders (customer_name, customer_phone, customer_line_id, shipping_method, shipping_store_name, shipping_store_number, subtotal, shipping_fee, discount, total_amount, status, coupon_code)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      orderData.customerInfo.name,
      orderData.customerInfo.phone,
      orderData.customerInfo.lineId || null,
      orderData.shippingMethod || '7-11',
      orderData.customerInfo.storeName || null,
      orderData.customerInfo.storeNumber || null,
      orderData.totals.subtotal,
      orderData.totals.shipping,
      orderData.totals.discount,
      orderData.totals.finalTotal,
      'pending',
      orderData.appliedCoupon?.coupon.code || null
    ]);

    const orderId = orderResult.lastID;

    // 生成可讀訂單號並更新回訂單表
    const orderNumber = `ORD-${new Date().getFullYear()}-${String(orderId).padStart(6, '0')}`;
    await dbAsync.run('UPDATE orders SET order_number = ? WHERE id = ?', [orderNumber, orderId]);

    // 更新通知物件中的訂單號
    orderDataForNotification.orderNumber = orderNumber;
    orderDataForNotification.orderId = orderId; // 確保 orderId 也可用

    // 2. 處理訂單項目和庫存
    for (const item of orderData.items) {
      // 插入訂單項目
      await dbAsync.run(`
        INSERT INTO order_items (order_id, product_id, variant_id, quantity, price, product_name, variant_value)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        orderId,
        item.product_id,
        item.variant_id || null,
        item.quantity,
        item.price,
        item.name,
        item.variant_value || null
      ]);

      // 更新庫存
      if (item.variant_id) {
        const variantStockResult = await dbAsync.run(
          'UPDATE product_variants SET stock = stock - ? WHERE id = ? AND stock >= ?',
          [item.quantity, item.variant_id, item.quantity]
        );
        if (variantStockResult.changes === 0) {
          throw new Error(`產品變體 ${item.name} (${item.variant_value}) 庫存不足`);
        }
      } else {
        const productStockResult = await dbAsync.run(
          'UPDATE products SET stock = stock - ? WHERE id = ? AND stock >= ?',
          [item.quantity, item.product_id, item.quantity]
        );
        if (productStockResult.changes === 0) {
          throw new Error(`產品 ${item.name} 庫存不足`);
        }
      }
    }

    await dbAsync.run('COMMIT');
    
    // 3. 發送Telegram通知 (成功後)
    try {
      const telegramSettings = await dbAsync.all(`
        SELECT key, value FROM system_settings 
        WHERE key IN ('telegram_bot_token', 'telegram_chat_id')
      `);
      
      const settings = {};
      telegramSettings.forEach(setting => {
        settings[setting.key] = setting.value;
      });

      const botToken = settings.telegram_bot_token;
      const chatId = settings.telegram_chat_id;

      if (botToken && chatId) {
        // 使用更新後的 orderDataForNotification
        await sendTelegramNotification(orderDataForNotification, botToken, chatId);
      }
    } catch (telegramError) {
      console.error('訂單已創建，但Telegram通知發送失敗:', telegramError);
    }

    res.json({ 
      success: true, 
      message: '訂單提交成功',
      orderId: orderId,
      orderNumber: orderNumber
    });

  } catch (error) {
    await dbAsync.run('ROLLBACK');
    console.error('提交訂單失敗，交易已回滾:', error);
    res.status(500).json({ error: error.message || '提交訂單失敗' });
  }
});

const sendTelegramNotification = async (orderData, botToken, chatId) => {
  try {
    const { orderNumber, customerInfo, items, totals, appliedCoupon } = orderData;
    
    const productList = items.map(item => {
      let productText = `• ${item.name} x${item.quantity} - NT$${item.total_price}`;
      if (item.variant_value) {
        productText += `\n  └ ${item.variant_type || '口味'}: ${item.variant_value}`;
      }
      return productText;
    }).join('\n');

    const message = `🛒 *新訂單通知*

📋 *訂單編號:* \`${orderNumber}\`
📅 *訂單時間:* ${new Date().toLocaleString('zh-TW')}

👤 *客戶資訊:*
• *姓名:* ${customerInfo.name}
• *電話:* \`${customerInfo.phone}\`
${customerInfo.lineId ? `• *Line ID:* ${customerInfo.lineId}` : ''}

🏪 *取貨門市:*
${customerInfo.storeName ? `• *店名:* ${customerInfo.storeName}` : ''}
${customerInfo.storeNumber ? `• *店號:* \`${customerInfo.storeNumber}\`` : ''}

🛍️ *訂購商品:*
${productList}

💰 *金額明細:*
• *商品小計:* NT$${totals.subtotal}
• *運費:* NT$${totals.shipping}
${appliedCoupon ? `• *優惠券:* ${appliedCoupon.coupon.code} (-NT$${totals.discount})` : ''}
• *總計:* *NT$${totals.finalTotal}*

請盡快處理此訂單！`;

    await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: message,
        parse_mode: 'Markdown'
      }),
    });

    console.log('Telegram通知發送成功！');
    return true;
  } catch (error) {
    console.error('Telegram通知發送失敗:', error);
    return false;
  }
};

// 獲取所有訂單（管理員用）
router.get('/admin/list', async (req, res) => {
  try {
    const { page = 1, limit = 20, status, startDate, endDate } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    let params = [];

    if (status && status !== 'all') {
      whereClause += ' AND o.status = ?';
      params.push(status);
    }

    if (startDate) {
      whereClause += ' AND DATE(o.created_at) >= ?';
      params.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND DATE(o.created_at) <= ?';
      params.push(endDate);
    }

    const orders = await dbAsync.all(`
      SELECT
        o.*,
        GROUP_CONCAT(
          oi.product_name || ' x' || oi.quantity ||
          CASE WHEN oi.variant_value THEN ' (' || oi.variant_value || ')' ELSE '' END,
          '; '
        ) as items_summary
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      ${whereClause}
      GROUP BY o.id
      ORDER BY o.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), offset]);

    const totalCount = await dbAsync.get(`
      SELECT COUNT(*) as count FROM orders o ${whereClause}
    `, params);

    res.json({
      success: true,
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    });
  } catch (error) {
    console.error('獲取訂單列表失敗:', error);
    res.status(500).json({ error: '獲取訂單列表失敗' });
  }
});

// 獲取訂單詳情
router.get('/admin/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const order = await dbAsync.get(`
      SELECT * FROM orders WHERE id = ?
    `, [id]);

    if (!order) {
      return res.status(404).json({ error: '訂單不存在' });
    }

    const items = await dbAsync.all(`
      SELECT
        oi.*,
        p.name as product_name,
        p.image_url
      FROM order_items oi
      LEFT JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = ?
    `, [id]);

    res.json({
      success: true,
      order: {
        ...order,
        items
      }
    });
  } catch (error) {
    console.error('獲取訂單詳情失敗:', error);
    res.status(500).json({ error: '獲取訂單詳情失敗' });
  }
});

// 更新訂單狀態
router.put('/admin/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['pending', 'confirmed', 'shipped', 'delivered', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: '無效的訂單狀態' });
    }

    await dbAsync.run(`
      UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?
    `, [status, id]);

    res.json({ success: true, message: '訂單狀態已更新' });
  } catch (error) {
    console.error('更新訂單狀態失敗:', error);
    res.status(500).json({ error: '更新訂單狀態失敗' });
  }
});

// 匯出訂單為 Excel
router.post('/admin/export', async (req, res) => {
  try {
    const { orderIds, startDate, endDate } = req.body;

    let whereClause = 'WHERE 1=1';
    let params = [];

    if (orderIds && orderIds.length > 0) {
      whereClause += ` AND o.id IN (${orderIds.map(() => '?').join(',')})`;
      params.push(...orderIds);
    } else {
      if (startDate) {
        whereClause += ' AND DATE(o.created_at) >= ?';
        params.push(startDate);
      }
      if (endDate) {
        whereClause += ' AND DATE(o.created_at) <= ?';
        params.push(endDate);
      }
    }

    const orders = await dbAsync.all(`
      SELECT
        o.*,
        GROUP_CONCAT(
          oi.product_name || ' x' || oi.quantity ||
          CASE WHEN oi.variant_value THEN ' (' || oi.variant_value || ')' ELSE '' END,
          '; '
        ) as items_summary
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      ${whereClause}
      GROUP BY o.id
      ORDER BY o.created_at DESC
    `, params);

    // 創建 Excel 工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('訂單列表');

    // 設置標題行
    worksheet.columns = [
      { header: '訂單編號', key: 'order_number', width: 20 },
      { header: '訂單時間', key: 'created_at', width: 20 },
      { header: '客戶姓名', key: 'customer_name', width: 15 },
      { header: '客戶電話', key: 'customer_phone', width: 15 },
      { header: '7-11店號', key: 'shipping_store_number', width: 15 },
      { header: '7-11店名', key: 'shipping_store_name', width: 25 },
      { header: '訂購商品', key: 'items_summary', width: 40 },
      { header: '小計', key: 'subtotal', width: 10 },
      { header: '運費', key: 'shipping_fee', width: 10 },
      { header: '折扣', key: 'discount', width: 10 },
      { header: '總金額', key: 'total_amount', width: 10 },
      { header: '狀態', key: 'status', width: 10 },
      { header: '備註', key: 'notes', width: 30 }
    ];

    // 添加數據
    orders.forEach(order => {
      worksheet.addRow({
        order_number: order.order_number,
        created_at: new Date(order.created_at).toLocaleString('zh-TW'),
        customer_name: order.customer_name,
        customer_phone: order.customer_phone,
        shipping_store_number: order.shipping_store_number,
        shipping_store_name: order.shipping_store_name,
        items_summary: order.items_summary,
        subtotal: order.subtotal,
        shipping_fee: order.shipping_fee,
        discount: order.discount,
        total_amount: order.total_amount,
        status: getStatusText(order.status),
        notes: order.notes || ''
      });
    });

    // 設置標題行樣式
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // 生成文件名
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const filename = `DOC${year}${day}${month}.xlsx`;

    // 設置響應頭
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // 寫入響應
    await workbook.xlsx.write(res);
    res.end();

  } catch (error) {
    console.error('匯出訂單失敗:', error);
    res.status(500).json({ error: '匯出訂單失敗' });
  }
});

// 輔助函數：獲取狀態文字
function getStatusText(status) {
  const statusMap = {
    'pending': '待處理',
    'confirmed': '已確認',
    'shipped': '已出貨',
    'delivered': '已送達',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
}

module.exports = router;