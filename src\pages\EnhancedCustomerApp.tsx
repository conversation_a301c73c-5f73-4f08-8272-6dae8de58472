import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ShoppingCart, Plus, Minus, X, ChevronLeft, ChevronRight, Loader2, Check } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { siteConfig } from '@/lib/config';
import { adminAPI, ordersAPI } from '@/lib/api';
import TypewriterAnnouncement from '@/components/TypewriterAnnouncement';
import SEO from '@/components/SEO';

// 類型定義
interface ProductVariant {
  id: number;
  product_id: number;
  variant_type: string;
  variant_value: string;
  price_modifier: number;
  stock: number;
  images: VariantImage[];
  bulkDiscounts: BulkDiscount[];
  basePrice: number;
}

interface VariantImage {
  id: number;
  image_url: string;
  sort_order: number;
  is_primary: boolean;
}

interface BulkDiscount {
  id: number;
  min_quantity: number;
  discount_type: 'percentage' | 'fixed';
  discount_value: number;
}

interface Flavor {
  id: number;
  name: string;
  available: boolean;
}

interface CartItem {
  variant: ProductVariant;
  flavors: Flavor[];
  quantity: number;
  originalPrice: number;
  discountedPrice: number;
  totalPrice: number;
  appliedDiscount?: BulkDiscount;
}

interface OrderForm {
  name: string;
  phone: string;
  email: string;
  store711: string;
  notes: string;
}

const EnhancedCustomerApp: React.FC = () => {
  const { toast } = useToast();
  
  // 狀態管理
  const [variants, setVariants] = useState<ProductVariant[]>([]);
  const [flavors, setFlavors] = useState<Flavor[]>([]);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [selectedFlavors, setSelectedFlavors] = useState<Flavor[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [orderForm, setOrderForm] = useState<OrderForm>({
    name: '',
    phone: '',
    email: '',
    store711: '',
    notes: ''
  });
  const [currentImageIndexes, setCurrentImageIndexes] = useState<Record<number, number>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderNumber, setOrderNumber] = useState('');
  const [loading, setLoading] = useState(true);

  // 載入數據
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // 載入產品變體和多件優惠
      const productsResponse = await adminAPI.getProducts();
      const allVariants: ProductVariant[] = [];
      
      for (const product of productsResponse.data.products) {
        const variantsResponse = await adminAPI.getProductVariants(product.id);
        
        for (const variant of variantsResponse.data) {
          // 載入變體圖片
          const imagesResponse = await adminAPI.getVariantImages(variant.id);
          
          // 載入多件優惠
          const discountsResponse = await adminAPI.getBulkDiscounts(variant.id);
          
          allVariants.push({
            ...variant,
            basePrice: product.price,
            images: imagesResponse.data,
            bulkDiscounts: discountsResponse.data
          });
        }
      }
      
      setVariants(allVariants);
      
      // 初始化圖片索引
      const imageIndexes: Record<number, number> = {};
      allVariants.forEach(variant => {
        imageIndexes[variant.id] = 0;
      });
      setCurrentImageIndexes(imageIndexes);
      
      // 模擬口味數據（實際應該從 API 獲取）
      const mockFlavors: Flavor[] = [
        { id: 1, name: '草莓', available: true },
        { id: 2, name: '芒果', available: true },
        { id: 3, name: '薄荷', available: true },
        { id: 4, name: '香草', available: true },
        { id: 5, name: '巧克力', available: true },
        { id: 6, name: '檸檬', available: false },
        { id: 7, name: '藍莓', available: true },
        { id: 8, name: '椰子', available: true },
      ];
      
      setFlavors(mockFlavors);
      
    } catch (error) {
      console.error('載入數據失敗:', error);
      toast({ 
        title: '載入失敗', 
        description: '無法載入產品數據，請重新整理頁面',
        variant: 'destructive' 
      });
      
      // 使用模擬數據作為後備
      const mockVariants: ProductVariant[] = [
        {
          id: 1,
          product_id: 1,
          variant_type: '規格',
          variant_value: '經典款',
          price_modifier: 0,
          stock: 50,
          basePrice: 350,
          images: [
            { id: 1, image_url: '/images/variant1-1.jpg', sort_order: 1, is_primary: true },
            { id: 2, image_url: '/images/variant1-2.jpg', sort_order: 2, is_primary: false }
          ],
          bulkDiscounts: [
            { id: 1, min_quantity: 2, discount_type: 'percentage', discount_value: 10 },
            { id: 2, min_quantity: 5, discount_type: 'percentage', discount_value: 20 }
          ]
        },
        {
          id: 2,
          product_id: 1,
          variant_type: '規格',
          variant_value: '豪華款',
          price_modifier: 100,
          stock: 30,
          basePrice: 350,
          images: [
            { id: 3, image_url: '/images/variant2-1.jpg', sort_order: 1, is_primary: true },
            { id: 4, image_url: '/images/variant2-2.jpg', sort_order: 2, is_primary: false },
            { id: 5, image_url: '/images/variant2-3.jpg', sort_order: 3, is_primary: false }
          ],
          bulkDiscounts: [
            { id: 3, min_quantity: 3, discount_type: 'fixed', discount_value: 100 }
          ]
        }
      ];
      
      setVariants(mockVariants);
      setCurrentImageIndexes({ 1: 0, 2: 0 });
      
      const mockFlavors: Flavor[] = [
        { id: 1, name: '草莓', available: true },
        { id: 2, name: '芒果', available: true },
        { id: 3, name: '薄荷', available: true },
        { id: 4, name: '香草', available: true },
        { id: 5, name: '巧克力', available: true },
        { id: 6, name: '檸檬', available: false },
        { id: 7, name: '藍莓', available: true },
        { id: 8, name: '椰子', available: true },
      ];
      
      setFlavors(mockFlavors);
    } finally {
      setLoading(false);
    }
  };

  // 計算價格（含優惠）
  const calculatePrice = (variant: ProductVariant, qty: number) => {
    const basePrice = variant.basePrice + variant.price_modifier;
    const originalPrice = basePrice * qty;
    let discountedPrice = originalPrice;
    let appliedDiscount: BulkDiscount | undefined;
    
    if (variant.bulkDiscounts && variant.bulkDiscounts.length > 0) {
      // 找到適用的最大優惠
      const applicableDiscount = variant.bulkDiscounts
        .filter(discount => qty >= discount.min_quantity)
        .sort((a, b) => b.min_quantity - a.min_quantity)[0];
      
      if (applicableDiscount) {
        appliedDiscount = applicableDiscount;
        if (applicableDiscount.discount_type === 'percentage') {
          discountedPrice = originalPrice * (1 - applicableDiscount.discount_value / 100);
        } else {
          discountedPrice = originalPrice - applicableDiscount.discount_value;
        }
      }
    }
    
    return {
      originalPrice,
      discountedPrice: Math.max(0, discountedPrice),
      appliedDiscount
    };
  };

  // 添加到購物車
  const addToCart = () => {
    if (!selectedVariant) {
      toast({ title: '請選擇品相', variant: 'destructive' });
      return;
    }
    
    if (selectedFlavors.length === 0) {
      toast({ title: '請至少選擇一種口味', variant: 'destructive' });
      return;
    }

    const priceCalculation = calculatePrice(selectedVariant, quantity);
    const cartItem: CartItem = {
      variant: selectedVariant,
      flavors: [...selectedFlavors],
      quantity,
      originalPrice: priceCalculation.originalPrice,
      discountedPrice: priceCalculation.discountedPrice,
      totalPrice: priceCalculation.discountedPrice,
      appliedDiscount: priceCalculation.appliedDiscount
    };

    setCart(prev => [...prev, cartItem]);
    setSelectedVariant(null);
    setSelectedFlavors([]);
    setQuantity(1);
    
    toast({ 
      title: '已加入購物車',
      description: priceCalculation.appliedDiscount 
        ? `已套用優惠：${priceCalculation.appliedDiscount.discount_type === 'percentage' 
            ? `${priceCalculation.appliedDiscount.discount_value}% 折扣` 
            : `折 NT$${priceCalculation.appliedDiscount.discount_value}`}`
        : undefined
    });
  };

  // 切換口味選擇
  const toggleFlavor = (flavor: Flavor) => {
    if (!flavor.available) return;
    
    setSelectedFlavors(prev => {
      const exists = prev.find(f => f.id === flavor.id);
      if (exists) {
        return prev.filter(f => f.id !== flavor.id);
      } else {
        return [...prev, flavor];
      }
    });
  };

  // 圖片導航
  const navigateImage = (variantId: number, direction: 'prev' | 'next') => {
    const variant = variants.find(v => v.id === variantId);
    if (!variant || variant.images.length <= 1) return;
    
    setCurrentImageIndexes(prev => {
      const currentIndex = prev[variantId] || 0;
      let newIndex;
      
      if (direction === 'next') {
        newIndex = (currentIndex + 1) % variant.images.length;
      } else {
        newIndex = currentIndex === 0 ? variant.images.length - 1 : currentIndex - 1;
      }
      
      return { ...prev, [variantId]: newIndex };
    });
  };

  // 購物車總計
  const cartTotal = cart.reduce((sum, item) => sum + item.totalPrice, 0);
  const cartItemCount = cart.reduce((sum, item) => sum + item.quantity, 0);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <SEO
        title={`${siteConfig.brandName} - 增強版產品選擇`}
        description="選擇您喜愛的產品和口味，享受多件優惠"
      />
      
      {/* 打字機公告 */}
      <div className="container mx-auto px-4 pt-4">
        <TypewriterAnnouncement />
      </div>
      
      {/* 頂部導航 */}
      <header className="bg-white shadow-sm sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-800">{siteConfig.brandName}</h1>
          <Button
            variant="outline"
            onClick={() => setShowCart(true)}
            className="relative"
          >
            <ShoppingCart className="h-5 w-5 mr-2" />
            購物車
            {cartItemCount > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0">
                {cartItemCount}
              </Badge>
            )}
          </Button>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* 品相選擇區 */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">選擇品相</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {variants.map((variant) => {
                    const currentImageIndex = currentImageIndexes[variant.id] || 0;
                    const currentImage = variant.images[currentImageIndex];
                    const priceCalculation = calculatePrice(variant, quantity);
                    
                    return (
                      <div
                        key={variant.id}
                        className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                          selectedVariant?.id === variant.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedVariant(variant)}
                      >
                        {/* 圖片輪播 */}
                        <div className="relative mb-4 group">
                          <img
                            src={currentImage?.image_url || '/images/placeholder.jpg'}
                            alt={variant.variant_value}
                            className="w-full h-48 object-cover rounded"
                            onError={(e) => {
                              e.currentTarget.src = '/images/placeholder.jpg';
                            }}
                          />
                          
                          {/* 圖片導航按鈕 */}
                          {variant.images.length > 1 && (
                            <>
                              <button
                                className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  navigateImage(variant.id, 'prev');
                                }}
                              >
                                <ChevronLeft className="h-4 w-4" />
                              </button>
                              <button
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  navigateImage(variant.id, 'next');
                                }}
                              >
                                <ChevronRight className="h-4 w-4" />
                              </button>
                              
                              {/* 圖片指示器 */}
                              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                                {variant.images.map((_, index) => (
                                  <button
                                    key={index}
                                    className={`w-2 h-2 rounded-full transition-colors ${
                                      index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                                    }`}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setCurrentImageIndexes(prev => ({
                                        ...prev,
                                        [variant.id]: index
                                      }));
                                    }}
                                  />
                                ))}
                              </div>
                            </>
                          )}
                        </div>
                        
                        <h3 className="font-semibold text-lg mb-2">{variant.variant_value}</h3>
                        <div className="mb-2">
                          <p className="text-blue-600 font-bold">
                            NT$ {variant.basePrice + variant.price_modifier}
                          </p>
                          {variant.price_modifier !== 0 && (
                            <p className="text-sm text-gray-500">
                              基本價格 + NT$ {variant.price_modifier}
                            </p>
                          )}
                        </div>
                        <p className="text-sm text-gray-500 mb-3">庫存: {variant.stock}</p>
                        
                        {/* 多件優惠顯示 */}
                        {variant.bulkDiscounts && variant.bulkDiscounts.length > 0 && (
                          <div className="bg-green-50 border border-green-200 rounded p-2 mb-2">
                            <p className="text-xs text-green-700 font-medium mb-1">🎉 多件優惠:</p>
                            {variant.bulkDiscounts.map((discount) => (
                              <p key={discount.id} className="text-xs text-green-600">
                                買 {discount.min_quantity} 件{' '}
                                {discount.discount_type === 'percentage' 
                                  ? `享 ${discount.discount_value}% 折扣` 
                                  : `折 NT$${discount.discount_value}`}
                              </p>
                            ))}
                          </div>
                        )}
                        
                        {/* 當前數量的價格預覽 */}
                        {selectedVariant?.id === variant.id && quantity > 1 && (
                          <div className="bg-blue-50 border border-blue-200 rounded p-2">
                            <p className="text-xs text-blue-700">
                              {quantity} 件價格: 
                              {priceCalculation.appliedDiscount ? (
                                <>
                                  <span className="line-through text-gray-500 ml-1">
                                    NT$ {priceCalculation.originalPrice}
                                  </span>
                                  <span className="text-blue-600 font-bold ml-1">
                                    NT$ {priceCalculation.discountedPrice}
                                  </span>
                                  <span className="text-green-600 text-xs ml-1">
                                    (已優惠 NT$ {priceCalculation.originalPrice - priceCalculation.discountedPrice})
                                  </span>
                                </>
                              ) : (
                                <span className="text-blue-600 font-bold ml-1">
                                  NT$ {priceCalculation.originalPrice}
                                </span>
                              )}
                            </p>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* 口味選擇區 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">選擇口味</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {flavors.map((flavor) => (
                    <Button
                      key={flavor.id}
                      variant={selectedFlavors.find(f => f.id === flavor.id) ? "default" : "outline"}
                      disabled={!flavor.available}
                      onClick={() => toggleFlavor(flavor)}
                      className="h-12"
                    >
                      {flavor.name}
                      {!flavor.available && <span className="ml-1 text-xs">(缺貨)</span>}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右側選擇摘要 */}
          <div className="space-y-6">
            <Card className="sticky top-24">
              <CardHeader>
                <CardTitle>您的選擇</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedVariant ? (
                  <>
                    <div>
                      <h3 className="font-semibold">{selectedVariant.variant_value}</h3>
                      <p className="text-blue-600">
                        NT$ {selectedVariant.basePrice + selectedVariant.price_modifier}
                      </p>
                    </div>

                    {selectedFlavors.length > 0 && (
                      <div>
                        <p className="font-medium mb-2">已選口味:</p>
                        <div className="flex flex-wrap gap-1">
                          {selectedFlavors.map((flavor) => (
                            <Badge key={flavor.id} variant="secondary">
                              {flavor.name}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex items-center space-x-3">
                      <Label>數量:</Label>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="w-8 text-center">{quantity}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setQuantity(quantity + 1)}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="border-t pt-3">
                      {(() => {
                        const priceCalc = calculatePrice(selectedVariant, quantity);
                        return (
                          <div>
                            {priceCalc.appliedDiscount ? (
                              <>
                                <p className="text-sm text-gray-500 line-through">
                                  原價: NT$ {priceCalc.originalPrice}
                                </p>
                                <p className="text-lg font-bold text-green-600">
                                  優惠價: NT$ {priceCalc.discountedPrice}
                                </p>
                                <p className="text-xs text-green-600">
                                  已套用 {priceCalc.appliedDiscount.discount_type === 'percentage'
                                    ? `${priceCalc.appliedDiscount.discount_value}% 折扣`
                                    : `NT$${priceCalc.appliedDiscount.discount_value} 折扣`}
                                </p>
                                <p className="text-xs text-green-600 font-medium">
                                  節省 NT$ {priceCalc.originalPrice - priceCalc.discountedPrice}
                                </p>
                              </>
                            ) : (
                              <p className="text-lg font-bold">
                                總計: NT$ {priceCalc.originalPrice}
                              </p>
                            )}
                          </div>
                        );
                      })()}
                    </div>

                    <Button
                      onClick={addToCart}
                      className="w-full"
                      disabled={selectedFlavors.length === 0}
                    >
                      加入購物車
                    </Button>
                  </>
                ) : (
                  <p className="text-gray-500 text-center py-8">
                    請選擇品相開始訂購
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 購物車對話框 */}
      <Dialog open={showCart} onOpenChange={setShowCart}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>購物車</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {cart.length === 0 ? (
              <p className="text-center text-gray-500 py-8">購物車是空的</p>
            ) : (
              <>
                {cart.map((item, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="font-semibold">{item.variant.variant_value}</h3>
                        <p className="text-sm text-gray-600">
                          口味: {item.flavors.map(f => f.name).join(', ')}
                        </p>
                        <p className="text-sm">數量: {item.quantity}</p>

                        {item.appliedDiscount ? (
                          <div>
                            <p className="text-sm text-gray-500 line-through">
                              原價: NT$ {item.originalPrice}
                            </p>
                            <p className="font-bold text-green-600">
                              優惠價: NT$ {item.totalPrice}
                            </p>
                            <p className="text-xs text-green-600">
                              {item.appliedDiscount.discount_type === 'percentage'
                                ? `${item.appliedDiscount.discount_value}% 折扣`
                                : `折 NT$${item.appliedDiscount.discount_value}`}
                            </p>
                          </div>
                        ) : (
                          <p className="font-bold text-blue-600">NT$ {item.totalPrice}</p>
                        )}
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setCart(prev => prev.filter((_, i) => i !== index))}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}

                <div className="border-t pt-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>商品總計:</span>
                      <span>NT$ {cart.reduce((sum, item) => sum + item.originalPrice, 0)}</span>
                    </div>
                    {cart.some(item => item.appliedDiscount) && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>優惠折扣:</span>
                        <span>-NT$ {cart.reduce((sum, item) => sum + (item.originalPrice - item.totalPrice), 0)}</span>
                      </div>
                    )}
                    <div className="flex justify-between items-center text-lg font-bold border-t pt-2">
                      <span>實付金額:</span>
                      <span>NT$ {cartTotal}</span>
                    </div>
                  </div>
                  <Button
                    onClick={() => {
                      setShowCart(false);
                      setShowOrderForm(true);
                    }}
                    className="w-full mt-4"
                  >
                    前往結帳
                  </Button>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* 訂單表單對話框 */}
      <Dialog open={showOrderForm} onOpenChange={setShowOrderForm}>
        <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>填寫訂購資訊</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="name">姓名 *</Label>
              <Input
                id="name"
                value={orderForm.name}
                onChange={(e) => setOrderForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="請輸入您的姓名"
                required
              />
            </div>

            <div>
              <Label htmlFor="phone">聯絡電話 *</Label>
              <Input
                id="phone"
                value={orderForm.phone}
                onChange={(e) => setOrderForm(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="請輸入聯絡電話"
                required
              />
            </div>

            <div>
              <Label htmlFor="email">電子郵件</Label>
              <Input
                id="email"
                type="email"
                value={orderForm.email}
                onChange={(e) => setOrderForm(prev => ({ ...prev, email: e.target.value }))}
                placeholder="請輸入電子郵件"
              />
            </div>

            <div>
              <Label htmlFor="store711">7-11 取貨門市 *</Label>
              <Input
                id="store711"
                value={orderForm.store711}
                onChange={(e) => setOrderForm(prev => ({ ...prev, store711: e.target.value }))}
                placeholder="請輸入門市代號或名稱"
                required
              />
            </div>

            <div>
              <Label htmlFor="notes">備註</Label>
              <Textarea
                id="notes"
                value={orderForm.notes}
                onChange={(e) => setOrderForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="特殊需求或備註事項"
                rows={3}
              />
            </div>

            <div className="border-t pt-4">
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span>商品總計:</span>
                  <span>NT$ {cart.reduce((sum, item) => sum + item.originalPrice, 0)}</span>
                </div>
                {cart.some(item => item.appliedDiscount) && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>優惠折扣:</span>
                    <span>-NT$ {cart.reduce((sum, item) => sum + (item.originalPrice - item.totalPrice), 0)}</span>
                  </div>
                )}
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>實付金額:</span>
                  <span>NT$ {cartTotal}</span>
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                付款方式: 7-11 取貨付款
              </p>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setShowOrderForm(false)}
                  className="flex-1"
                >
                  取消
                </Button>
                <Button
                  onClick={async () => {
                    if (!orderForm.name || !orderForm.phone || !orderForm.store711) {
                      toast({ title: '請填寫必要資訊', variant: 'destructive' });
                      return;
                    }

                    setIsSubmitting(true);

                    try {
                      // 生成訂單號
                      const now = new Date();
                      const year = now.getFullYear();
                      const month = String(now.getMonth() + 1).padStart(2, '0');
                      const day = String(now.getDate()).padStart(2, '0');
                      const hour = String(now.getHours()).padStart(2, '0');
                      const minute = String(now.getMinutes()).padStart(2, '0');
                      const generatedOrderNumber = `ORD${year}${day}${month}${hour}${minute}`;

                      // 準備訂單數據
                      const orderData = {
                        orderNumber: generatedOrderNumber,
                        customerInfo: {
                          name: orderForm.name,
                          phone: orderForm.phone,
                          email: orderForm.email,
                          storeNumber: orderForm.store711,
                          storeName: orderForm.store711,
                        },
                        items: cart.map(item => ({
                          product_id: item.variant.product_id,
                          variant_id: item.variant.id,
                          name: item.variant.variant_value,
                          quantity: item.quantity,
                          price: item.variant.basePrice + item.variant.price_modifier,
                          total_price: item.totalPrice,
                          original_price: item.originalPrice,
                          discount_applied: item.appliedDiscount ? {
                            type: item.appliedDiscount.discount_type,
                            value: item.appliedDiscount.discount_value,
                            min_quantity: item.appliedDiscount.min_quantity
                          } : null,
                          variant_value: item.flavors.map(f => f.name).join(', '),
                          variant_type: '口味'
                        })),
                        totals: {
                          subtotal: cart.reduce((sum, item) => sum + item.originalPrice, 0),
                          shipping: 0,
                          discount: cart.reduce((sum, item) => sum + (item.originalPrice - item.totalPrice), 0),
                          finalTotal: cartTotal
                        },
                        shippingMethod: '7-11',
                        notes: orderForm.notes
                      };

                      const response = await ordersAPI.submitOrder(orderData);

                      if (response.data.success) {
                        setOrderNumber(response.data.orderNumber);
                        setOrderSuccess(true);
                        setShowOrderForm(false);
                        setCart([]);
                        setOrderForm({
                          name: '',
                          phone: '',
                          email: '',
                          store711: '',
                          notes: ''
                        });

                        toast({
                          title: '訂單提交成功',
                          description: `訂單編號：${response.data.orderNumber}`
                        });
                      }
                    } catch (error: any) {
                      console.error('訂單提交失敗:', error);
                      toast({
                        title: '訂單提交失敗',
                        description: error.response?.data?.error || error.message,
                        variant: 'destructive'
                      });
                    } finally {
                      setIsSubmitting(false);
                    }
                  }}
                  className="flex-1"
                  disabled={!orderForm.name || !orderForm.phone || !orderForm.store711 || isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      提交中...
                    </>
                  ) : (
                    '確認訂購'
                  )}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 訂單成功對話框 */}
      <Dialog open={orderSuccess} onOpenChange={setOrderSuccess}>
        <DialogContent className="max-w-md">
          <div className="text-center py-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
              <Check className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-green-600 mb-4">
              訂購成功！
            </h3>
            <p className="text-gray-600 mb-4">
              感謝您的訂購，我們將盡快為您準備商品
            </p>
            <div className="bg-gray-50 p-4 rounded-lg mb-4">
              <p className="text-sm text-gray-500">訂單編號</p>
              <p className="font-mono font-bold text-lg">{orderNumber}</p>
            </div>
            <p className="text-sm text-gray-500 mb-6">
              請保存此訂單編號，我們會透過 Telegram 發送訂單確認通知
            </p>
            <Button
              onClick={() => setOrderSuccess(false)}
              className="w-full"
            >
              繼續購物
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EnhancedCustomerApp;
