import React from 'react';
import { siteConfig } from '@/lib/config';

const ConfigTest: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">環境變量配置測試</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">網站基本信息</h2>
          <div className="space-y-2">
            <p><strong>網站名稱:</strong> {siteConfig.name}</p>
            <p><strong>網站標題:</strong> {siteConfig.title}</p>
            <p><strong>網站描述:</strong> {siteConfig.description}</p>
            <p><strong>網站域名:</strong> {siteConfig.domain}</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">品牌信息</h2>
          <div className="space-y-2">
            <p><strong>品牌名稱:</strong> {siteConfig.brandName}</p>
            <p><strong>品牌標語:</strong> {siteConfig.brandSlogan}</p>
            <p><strong>品牌描述:</strong> {siteConfig.brandDescription}</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">聯繫信息</h2>
          <div className="space-y-2">
            <p><strong>LINE:</strong> {siteConfig.contact.line}</p>
            <p><strong>Email:</strong> {siteConfig.contact.email}</p>
            <p><strong>支援Email:</strong> {siteConfig.contact.supportEmail}</p>
            <p><strong>服務時間:</strong> {siteConfig.contact.hours}</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">產品分類</h2>
          <div className="space-y-2">
            <p><strong>主機:</strong> {siteConfig.categories.host}</p>
            <p><strong>煙彈:</strong> {siteConfig.categories.cartridge}</p>
            <p><strong>拋棄式:</strong> {siteConfig.categories.disposable}</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">服務特色</h2>
          <div className="space-y-2">
            <p><strong>配送:</strong> {siteConfig.features.delivery.title} - {siteConfig.features.delivery.description}</p>
            <p><strong>品質:</strong> {siteConfig.features.quality.title} - {siteConfig.features.quality.description}</p>
            <p><strong>運費:</strong> {siteConfig.features.shipping.title} - {siteConfig.features.shipping.description}</p>
            <p><strong>服務:</strong> {siteConfig.features.service.title} - {siteConfig.features.service.description}</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">其他配置</h2>
          <div className="space-y-2">
            <p><strong>版權年份:</strong> {siteConfig.copyright.year}</p>
            <p><strong>版權文字:</strong> {siteConfig.copyright.text}</p>
            <p><strong>年齡驗證標題:</strong> {siteConfig.ageVerification.title}</p>
          </div>
        </div>
      </div>

      <div className="mt-8 p-4 bg-green-100 rounded-lg">
        <h3 className="text-lg font-semibold text-green-800 mb-2">✅ 配置狀態</h3>
        <p className="text-green-700">
          如果您能看到上面的所有配置信息，說明環境變量已經正確加載！
        </p>
      </div>
    </div>
  );
};

export default ConfigTest;
