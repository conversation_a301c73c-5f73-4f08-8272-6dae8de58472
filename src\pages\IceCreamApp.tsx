import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ShoppingCart, Plus, Minus, X, MapPin, Loader2, Check } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { siteConfig } from '@/lib/config';
import { ordersAPI } from '@/lib/api';
import TypewriterAnnouncement from '@/components/TypewriterAnnouncement';
import SEO from '@/components/SEO';

// 類型定義
interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  stock: number;
  category: string;
  image?: string;
}

interface Topping {
  id: number;
  name: string;
  price: number;
  category: string;
  available: boolean;
}

interface CartItem {
  product: Product;
  toppings: Topping[];
  quantity: number;
  totalPrice: number;
  id: string;
}

interface OrderForm {
  name: string;
  phone: string;
  email: string;
  store711: string;
  notes: string;
}

const IceCreamApp: React.FC = () => {
  const { toast } = useToast();

  // 狀態管理
  const [products, setProducts] = useState<Product[]>([]);
  const [toppings, setToppings] = useState<Topping[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedToppings, setSelectedToppings] = useState<Topping[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [orderForm, setOrderForm] = useState<OrderForm>({
    name: '',
    phone: '',
    email: '',
    store711: '',
    notes: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderNumber, setOrderNumber] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // 模擬數據
  useEffect(() => {
    // 模擬冰淇淋產品
    const mockProducts: Product[] = [
      {
        id: 1,
        name: '經典香草',
        description: '使用馬達加斯加香草豆製作的經典口味',
        price: 120,
        stock: 50,
        category: 'classic',
        image: '/images/vanilla.jpg'
      },
      {
        id: 2,
        name: '濃郁巧克力',
        description: '比利時進口可可製作的濃郁巧克力冰淇淋',
        price: 130,
        stock: 45,
        category: 'classic',
        image: '/images/chocolate.jpg'
      },
      {
        id: 3,
        name: '草莓戀人',
        description: '新鮮草莓果肉與奶香完美結合',
        price: 140,
        stock: 30,
        category: 'fruit',
        image: '/images/strawberry.jpg'
      },
      {
        id: 4,
        name: '開心果',
        description: '西西里島開心果製作的義式經典',
        price: 160,
        stock: 25,
        category: 'premium',
        image: '/images/pistachio.jpg'
      },
      {
        id: 5,
        name: '芒果雪酪',
        description: '台灣愛文芒果製作的清爽雪酪',
        price: 135,
        stock: 35,
        category: 'sorbet',
        image: '/images/mango.jpg'
      },
      {
        id: 6,
        name: '提拉米蘇',
        description: '義式經典甜點風味冰淇淋',
        price: 150,
        stock: 20,
        category: 'premium',
        image: '/images/tiramisu.jpg'
      }
    ];

    // 模擬配料
    const mockToppings: Topping[] = [
      { id: 1, name: '巧克力脆片', price: 15, category: 'chocolate', available: true },
      { id: 2, name: '焦糖醬', price: 10, category: 'sauce', available: true },
      { id: 3, name: '新鮮草莓', price: 20, category: 'fruit', available: true },
      { id: 4, name: '烤杏仁片', price: 18, category: 'nuts', available: true },
      { id: 5, name: '奧利奧餅乾', price: 12, category: 'cookie', available: true },
      { id: 6, name: '蜂蜜', price: 8, category: 'sauce', available: true },
      { id: 7, name: '藍莓', price: 22, category: 'fruit', available: true },
      { id: 8, name: '開心果碎', price: 25, category: 'nuts', available: false },
      { id: 9, name: '抹茶粉', price: 15, category: 'powder', available: true },
      { id: 10, name: '椰子絲', price: 12, category: 'tropical', available: true }
    ];

    setProducts(mockProducts);
    setToppings(mockToppings);
  }, []);

  // 產品分類
  const categories = [
    { id: 'all', name: siteConfig.ui.product.allCategories },
    { id: 'classic', name: '經典口味' },
    { id: 'fruit', name: '水果系列' },
    { id: 'premium', name: '精選系列' },
    { id: 'sorbet', name: '雪酪系列' }
  ];

  // 配料分類
  const toppingCategories = [
    { id: 'all', name: '全部配料' },
    { id: 'chocolate', name: '巧克力系列' },
    { id: 'fruit', name: '新鮮水果' },
    { id: 'nuts', name: '堅果類' },
    { id: 'sauce', name: '醬料' },
    { id: 'cookie', name: '餅乾類' },
    { id: 'powder', name: '粉類' },
    { id: 'tropical', name: '熱帶風味' }
  ];

  // 篩選產品
  const filteredProducts = selectedCategory === 'all'
    ? products
    : products.filter(product => product.category === selectedCategory);

  // 計算價格
  const calculateItemPrice = () => {
    if (!selectedProduct) return 0;
    const productPrice = selectedProduct.price;
    const toppingsPrice = selectedToppings.reduce((sum, topping) => sum + topping.price, 0);
    return (productPrice + toppingsPrice) * quantity;
  };

  // 添加到購物車
  const addToCart = () => {
    if (!selectedProduct) {
      toast({
        title: siteConfig.ui.messages.selectProduct,
        variant: 'destructive'
      });
      return;
    }

    const cartItem: CartItem = {
      id: `${selectedProduct.id}-${Date.now()}`,
      product: selectedProduct,
      toppings: [...selectedToppings],
      quantity,
      totalPrice: calculateItemPrice()
    };

    setCart(prev => [...prev, cartItem]);
    setSelectedProduct(null);
    setSelectedToppings([]);
    setQuantity(1);

    toast({
      title: siteConfig.ui.notifications.added,
      description: `${selectedProduct.name} ${siteConfig.ui.notifications.added}`
    });
  };

  // 切換配料選擇
  const toggleTopping = (topping: Topping) => {
    if (!topping.available) return;

    setSelectedToppings(prev => {
      const exists = prev.find(t => t.id === topping.id);
      if (exists) {
        return prev.filter(t => t.id !== topping.id);
      } else {
        return [...prev, topping];
      }
    });
  };

  // 購物車總計
  const cartTotal = cart.reduce((sum, item) => sum + item.totalPrice, 0);
  const cartItemCount = cart.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <div className="min-h-screen bg-gray-50">
      <SEO
        title={`${siteConfig.brandName} - ${siteConfig.brandSlogan}`}
        description={siteConfig.brandDescription}
      />

      {/* 打字機公告 */}
      <div className="container mx-auto px-4 pt-4">
        <TypewriterAnnouncement />
      </div>

      {/* 頂部導航 */}
      <header className="bg-white shadow-sm sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex flex-col">
            <h1 className="text-2xl font-bold text-gray-800 tracking-wider">
              {siteConfig.brandName.toUpperCase()}
            </h1>
            <span className="text-xs text-gray-500 tracking-widest font-light">
              THE SEA IS IMMEASURABLE
            </span>
          </div>
          <Button
            variant="outline"
            onClick={() => setShowCart(true)}
            className="relative bg-gray-900 text-white hover:bg-orange-500 border-none"
          >
            <ShoppingCart className="h-5 w-5 mr-2" />
            {siteConfig.ui.cart}
            {cartItemCount > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 bg-orange-500">
                {cartItemCount}
              </Badge>
            )}
          </Button>
        </div>
      </header>

      {/* 主視覺區塊 */}
      <section className="bg-gradient-to-br from-gray-800 to-gray-900 text-white py-16 text-center">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl md:text-5xl font-light tracking-wider mb-4">
            義式手工冰淇淋
          </h1>
          <p className="text-lg font-light opacity-90 tracking-wide">
            精選義大利進口原料，手工製作的頂級冰淇淋
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">
        {/* 產品分類篩選 */}
        <div className="flex justify-center gap-4 mb-8 flex-wrap">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              onClick={() => setSelectedCategory(category.id)}
              className={`${
                selectedCategory === category.id
                  ? 'bg-gray-900 text-white'
                  : 'border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white'
              } uppercase tracking-wider`}
            >
              {category.name}
            </Button>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">

          {/* 產品選擇區 */}
          <div className="lg:col-span-2 space-y-6">
            <div>
              <h2 className="text-xl font-light tracking-wider mb-6 text-center">
                {siteConfig.ui.product.selection}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {filteredProducts.map((product) => (
                  <div
                    key={product.id}
                    className={`bg-white border-2 p-6 cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${
                      selectedProduct?.id === product.id
                        ? 'border-orange-500 bg-orange-50'
                        : 'border-gray-200 hover:border-orange-300'
                    }`}
                    onClick={() => setSelectedProduct(product)}
                  >
                    {selectedProduct?.id === product.id && (
                      <div className="absolute top-4 right-4 bg-orange-500 text-white w-8 h-8 flex items-center justify-center font-bold">
                        ✓
                      </div>
                    )}

                    <h3 className="text-xl font-medium mb-2 text-gray-800 tracking-wide">
                      {product.name}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                      {product.description}
                    </p>
                    <div className="flex justify-between items-center">
                      <span className="text-xl font-semibold text-orange-500 tracking-wide">
                        NT$ {product.price}
                      </span>
                      <span className="text-sm text-gray-500">
                        {siteConfig.ui.product.stock}: {product.stock}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 配料選擇區 */}
            {selectedProduct && (
              <div>
                <h2 className="text-xl font-light tracking-wider mb-6 text-center">
                  {siteConfig.ui.product.toppingSelection}
                </h2>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {toppings.map((topping) => (
                    <Button
                      key={topping.id}
                      variant={selectedToppings.find(t => t.id === topping.id) ? "default" : "outline"}
                      disabled={!topping.available}
                      onClick={() => toggleTopping(topping)}
                      className={`h-auto py-3 px-4 flex flex-col items-center gap-1 ${
                        selectedToppings.find(t => t.id === topping.id)
                          ? 'bg-gray-900 text-white'
                          : 'border-gray-300 text-gray-700 hover:bg-gray-900 hover:text-white'
                      }`}
                    >
                      <span className="text-sm font-medium">{topping.name}</span>
                      <span className="text-xs opacity-75">+NT$ {topping.price}</span>
                      {!topping.available && (
                        <span className="text-xs text-red-500">({siteConfig.ui.messages.outOfStock})</span>
                      )}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 右側選擇摘要 */}
          <div className="space-y-6">
            <div className="bg-white border border-gray-200 p-6 sticky top-24">
              <h3 className="text-lg font-light tracking-wider mb-4 text-center">
                {siteConfig.ui.order.summary}
              </h3>

              {selectedProduct ? (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-800">{selectedProduct.name}</h4>
                    <p className="text-orange-500 font-semibold">NT$ {selectedProduct.price}</p>
                  </div>

                  {selectedToppings.length > 0 && (
                    <div>
                      <p className="font-medium mb-2 text-gray-700">已選配料:</p>
                      <div className="space-y-1">
                        {selectedToppings.map((topping) => (
                          <div key={topping.id} className="flex justify-between text-sm">
                            <span>{topping.name}</span>
                            <span>+NT$ {topping.price}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <Label className="text-gray-700">{siteConfig.ui.quantity}:</Label>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        className="w-8 h-8 p-0"
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <span className="w-8 text-center font-medium">{quantity}</span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setQuantity(quantity + 1)}
                        className="w-8 h-8 p-0"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center text-lg font-semibold">
                      <span>{siteConfig.ui.total}:</span>
                      <span className="text-orange-500">NT$ {calculateItemPrice()}</span>
                    </div>
                  </div>

                  <Button
                    onClick={addToCart}
                    className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3"
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    {siteConfig.ui.addToCart}
                  </Button>
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">
                  {siteConfig.ui.messages.selectProduct}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 購物車側邊欄 */}
      <Dialog open={showCart} onOpenChange={setShowCart}>
        <DialogContent className="max-w-md h-[90vh] flex flex-col p-0">
          <DialogHeader className="bg-gray-900 text-white p-6">
            <DialogTitle className="text-xl font-light tracking-wider">
              {siteConfig.ui.cart}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto p-6">
            {cart.length === 0 ? (
              <p className="text-center text-gray-500 py-8">
                {siteConfig.ui.messages.emptyCart}
              </p>
            ) : (
              <div className="space-y-4">
                {cart.map((item) => (
                  <div key={item.id} className="border-b pb-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium text-gray-800">{item.product.name}</h4>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setCart(prev => prev.filter(i => i.id !== item.id))}
                        className="text-red-500 hover:text-red-700 p-1"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    {item.toppings.length > 0 && (
                      <p className="text-sm text-gray-600 mb-2">
                        配料: {item.toppings.map(t => t.name).join(', ')}
                      </p>
                    )}

                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setCart(prev => prev.map(i =>
                              i.id === item.id && i.quantity > 1
                                ? { ...i, quantity: i.quantity - 1, totalPrice: (i.totalPrice / i.quantity) * (i.quantity - 1) }
                                : i
                            ));
                          }}
                          className="w-8 h-8 p-0"
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <span className="w-8 text-center">{item.quantity}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setCart(prev => prev.map(i =>
                              i.id === item.id
                                ? { ...i, quantity: i.quantity + 1, totalPrice: (i.totalPrice / i.quantity) * (i.quantity + 1) }
                                : i
                            ));
                          }}
                          className="w-8 h-8 p-0"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                      <span className="font-semibold text-orange-500">
                        NT$ {item.totalPrice}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {cart.length > 0 && (
            <div className="bg-gray-50 p-6 border-t">
              <div className="flex justify-between items-center mb-4">
                <span className="text-lg font-medium text-gray-700 tracking-wide">
                  {siteConfig.ui.total}:
                </span>
                <span className="text-2xl font-semibold text-orange-500">
                  NT$ {cartTotal}
                </span>
              </div>
              <Button
                onClick={() => {
                  setShowCart(false);
                  setShowOrderForm(true);
                }}
                className="w-full bg-gray-900 hover:bg-orange-500 text-white py-3 tracking-wider uppercase"
              >
                {siteConfig.ui.checkout}
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 訂單表單 */}
      <Dialog open={showOrderForm} onOpenChange={setShowOrderForm}>
        <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-light tracking-wider text-center">
              {siteConfig.ui.order.info}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="name" className="text-gray-700 font-medium">
                {siteConfig.ui.form.name} *
              </Label>
              <Input
                id="name"
                value={orderForm.name}
                onChange={(e) => setOrderForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder={siteConfig.ui.form.placeholders.name}
                className="mt-1"
                required
              />
            </div>

            <div>
              <Label htmlFor="phone" className="text-gray-700 font-medium">
                {siteConfig.ui.form.phone} *
              </Label>
              <Input
                id="phone"
                value={orderForm.phone}
                onChange={(e) => setOrderForm(prev => ({ ...prev, phone: e.target.value }))}
                placeholder={siteConfig.ui.form.placeholders.phone}
                className="mt-1"
                required
              />
            </div>

            <div>
              <Label htmlFor="email" className="text-gray-700 font-medium">
                {siteConfig.ui.form.email}
              </Label>
              <Input
                id="email"
                type="email"
                value={orderForm.email}
                onChange={(e) => setOrderForm(prev => ({ ...prev, email: e.target.value }))}
                placeholder={siteConfig.ui.form.placeholders.email}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="store711" className="text-gray-700 font-medium">
                {siteConfig.ui.form.store} *
              </Label>
              <div className="flex space-x-2 mt-1">
                <Input
                  id="store711"
                  value={orderForm.store711}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, store711: e.target.value }))}
                  placeholder={siteConfig.ui.form.placeholders.store}
                  className="flex-1"
                  required
                />
                <Button variant="outline" size="sm" className="px-3">
                  <MapPin className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div>
              <Label htmlFor="notes" className="text-gray-700 font-medium">
                {siteConfig.ui.form.notes}
              </Label>
              <Textarea
                id="notes"
                value={orderForm.notes}
                onChange={(e) => setOrderForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder={siteConfig.ui.form.placeholders.notes}
                className="mt-1"
                rows={3}
              />
            </div>

            <div className="border-t pt-4">
              <div className="flex justify-between items-center mb-4">
                <span className="font-medium text-gray-700">{siteConfig.ui.total}:</span>
                <span className="text-xl font-bold text-orange-500">NT$ {cartTotal}</span>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                {siteConfig.ui.order.paymentMethod}: {siteConfig.ui.order.paymentCOD}
              </p>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setShowOrderForm(false)}
                  className="flex-1"
                >
                  {siteConfig.ui.cancel}
                </Button>
                <Button
                  onClick={async () => {
                    if (!orderForm.name || !orderForm.phone || !orderForm.store711) {
                      toast({
                        title: siteConfig.ui.validation.required,
                        variant: 'destructive'
                      });
                      return;
                    }

                    setIsSubmitting(true);

                    try {
                      // 生成訂單號
                      const now = new Date();
                      const year = now.getFullYear();
                      const month = String(now.getMonth() + 1).padStart(2, '0');
                      const day = String(now.getDate()).padStart(2, '0');
                      const hour = String(now.getHours()).padStart(2, '0');
                      const minute = String(now.getMinutes()).padStart(2, '0');
                      const generatedOrderNumber = `ORD${year}${day}${month}${hour}${minute}`;

                      // 準備訂單數據
                      const orderData = {
                        orderNumber: generatedOrderNumber,
                        customerInfo: {
                          name: orderForm.name,
                          phone: orderForm.phone,
                          email: orderForm.email,
                          storeNumber: orderForm.store711,
                          storeName: orderForm.store711,
                        },
                        items: cart.map(item => ({
                          product_id: item.product.id,
                          name: item.product.name,
                          quantity: item.quantity,
                          price: item.product.price,
                          total_price: item.totalPrice,
                          variant_value: item.toppings.map(t => t.name).join(', '),
                          variant_type: '配料'
                        })),
                        totals: {
                          subtotal: cartTotal,
                          shipping: 0,
                          discount: 0,
                          finalTotal: cartTotal
                        },
                        shippingMethod: '7-11',
                        notes: orderForm.notes
                      };

                      const response = await ordersAPI.submitOrder(orderData);

                      if (response.data.success) {
                        setOrderNumber(response.data.orderNumber);
                        setOrderSuccess(true);
                        setShowOrderForm(false);
                        setCart([]);
                        setOrderForm({
                          name: '',
                          phone: '',
                          email: '',
                          store711: '',
                          notes: ''
                        });

                        toast({
                          title: siteConfig.ui.messages.successTitle,
                          description: `${siteConfig.ui.messages.orderNumber}：${response.data.orderNumber}`
                        });
                      }
                    } catch (error: any) {
                      console.error('訂單提交失敗:', error);
                      toast({
                        title: siteConfig.ui.messages.errorTitle,
                        description: error.response?.data?.error || siteConfig.ui.messages.errorMessage,
                        variant: 'destructive'
                      });
                    } finally {
                      setIsSubmitting(false);
                    }
                  }}
                  className="flex-1 bg-gray-900 hover:bg-orange-500"
                  disabled={!orderForm.name || !orderForm.phone || !orderForm.store711 || isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {siteConfig.ui.messages.loading}
                    </>
                  ) : (
                    siteConfig.ui.confirmOrder
                  )}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 訂單成功對話框 */}
      <Dialog open={orderSuccess} onOpenChange={setOrderSuccess}>
        <DialogContent className="max-w-md">
          <div className="text-center py-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
              <Check className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-xl font-light tracking-wider text-green-600 mb-4">
              {siteConfig.ui.messages.successTitle}
            </h3>
            <p className="text-gray-600 mb-4">
              {siteConfig.ui.messages.successMessage}
            </p>
            <div className="bg-gray-50 p-4 rounded-lg mb-4">
              <p className="text-sm text-gray-500">{siteConfig.ui.messages.orderNumber}</p>
              <p className="font-mono font-bold text-lg">{orderNumber}</p>
            </div>
            <p className="text-sm text-gray-500 mb-6">
              請保存此訂單編號，我們會透過 Telegram 發送訂單確認通知
            </p>
            <Button
              onClick={() => setOrderSuccess(false)}
              className="w-full bg-gray-900 hover:bg-orange-500"
            >
              {siteConfig.ui.continueShopping}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default IceCreamApp;