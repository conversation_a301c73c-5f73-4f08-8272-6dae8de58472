
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#2C3E50">
    <title>海水不可斗量 - 義式手工冰淇淋</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Noto+Sans+TC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #2C3E50;
            --secondary: #34495E;
            --accent: #E67E22;
            --gold: #F39C12;
            --light: #ECF0F1;
            --dark: #1A1A1A;
            --gray: #7F8C8D;
            --white: #FFFFFF;
            --shadow: 0 2px 20px rgba(0,0,0,0.08);
            --shadow-hover: 0 8px 30px rgba(0,0,0,0.12);
            --success: #27AE60;
            --danger: #E74C3C;
            --border: #E0E0E0;
            --bg: #F8F9FA;
        }

        body {
            font-family: 'Noto Sans TC', 'Montserrat', sans-serif;
            background-color: #FAFAFA;
            color: var(--dark);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 頂部導航 - 極簡設計 */
        .navbar {
            background: var(--white);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 1px 0 rgba(0,0,0,0.05);
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.2rem;
            font-weight: 700;
            letter-spacing: 2px;
            color: var(--primary);
            text-decoration: none;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            line-height: 1.2;
        }

        .logo-subtitle {
            font-size: 0.7rem;
            font-weight: 300;
            letter-spacing: 1px;
            opacity: 0.7;
            margin-top: 2px;
        }

        .cart-button {
            position: relative;
            background: var(--dark);
            color: var(--white);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .cart-button:hover {
            background: var(--accent);
        }

        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--accent);
            color: var(--white);
            min-width: 20px;
            height: 20px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0 6px;
        }

        /* 主視覺區塊 - 極簡奢華 */
        .hero {
            margin-top: 60px;
            background: var(--dark);
            color: var(--white);
            padding: 4rem 1rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(44,62,80,0.9) 0%, rgba(52,73,94,0.9) 100%);
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero h1 {
            font-family: 'Montserrat', sans-serif;
            font-size: 2.5rem;
            font-weight: 300;
            letter-spacing: 3px;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.1rem;
            font-weight: 300;
            opacity: 0.9;
            letter-spacing: 1px;
        }

        /* 主要內容區 */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 3rem 1rem;
        }

        /* 產品區塊 - 優雅卡片 */
        .section {
            margin-bottom: 4rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.8rem;
            font-weight: 300;
            letter-spacing: 2px;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .section-subtitle {
            color: var(--gray);
            font-size: 1rem;
            font-weight: 300;
        }

        /* 產品網格 - 精緻佈局 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background: var(--white);
            border: 1px solid #E0E0E0;
            padding: 2rem;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
            border-color: var(--accent);
        }

        .product-card.selected {
            border-color: var(--accent);
            background: #FFF9F5;
        }

        .product-card.selected::after {
            content: '✓';
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--accent);
            color: var(--white);
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .product-name {
            font-size: 1.3rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: var(--primary);
            letter-spacing: 0.5px;
        }

        .product-description {
            font-size: 0.9rem;
            color: var(--gray);
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .product-price {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--accent);
            letter-spacing: 1px;
        }

        /* 配料分類 - 簡約按鈕 */
        .category-filters {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }

        .category-btn {
            background: transparent;
            border: 1px solid var(--primary);
            color: var(--primary);
            padding: 0.75rem 2rem;
            font-size: 0.9rem;
            font-weight: 500;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }

        .category-btn:hover {
            background: var(--primary);
            color: var(--white);
        }

        .category-btn.active {
            background: var(--primary);
            color: var(--white);
        }

        /* 購物車側邊欄 - 優雅設計 */
        .cart-sidebar {
            position: fixed;
            right: -450px;
            top: 0;
            width: 450px;
            height: 100vh;
            background: var(--white);
            box-shadow: -5px 0 30px rgba(0,0,0,0.1);
            transition: right 0.4s ease;
            z-index: 2000;
            display: flex;
            flex-direction: column;
        }

        .cart-sidebar.open {
            right: 0;
        }

        .cart-header {
            background: var(--dark);
            color: var(--white);
            padding: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .cart-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.3rem;
            font-weight: 300;
            letter-spacing: 2px;
        }

        .cart-close {
            background: none;
            border: none;
            color: var(--white);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            transition: opacity 0.3s;
        }

        .cart-close:hover {
            opacity: 0.7;
        }

        .cart-content {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }

        .cart-empty {
            text-align: center;
            color: var(--gray);
            padding: 3rem 0;
        }

        .cart-item {
            border-bottom: 1px solid #E0E0E0;
            padding: 1.5rem 0;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .cart-item-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 0.5rem;
        }

        .cart-item-name {
            font-weight: 500;
            font-size: 1.1rem;
            color: var(--primary);
        }

        .cart-item-price {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            color: var(--accent);
        }

        .cart-item-details {
            font-size: 0.9rem;
            color: var(--gray);
            line-height: 1.6;
        }

        /* 購物車項目操作 */
        .cart-item-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
        }

        .quantity-control {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: var(--bg);
            border: 1px solid var(--border);
            padding: 0.25rem;
        }

        .quantity-control button {
            background: none;
            border: none;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--secondary);
            font-size: 1rem;
            transition: all 0.2s;
        }

        .quantity-control button:hover {
            background: var(--white);
            color: var(--accent);
        }

        .quantity-control span {
            min-width: 30px;
            text-align: center;
            font-weight: 600;
        }

        .remove-btn {
            background: none;
            border: none;
            color: var(--danger);
            cursor: pointer;
            padding: 0.5rem;
            transition: all 0.2s;
        }

        .remove-btn:hover {
            color: #C0392B;
        }

        /* 當前選擇樣式 */
        .cart-item.current-selection {
            background: #FFF9F5;
            border: 1px solid var(--accent);
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .add-to-cart-btn {
            width: 100%;
            background: var(--accent);
            color: var(--white);
            border: none;
            padding: 0.75rem;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            margin-top: 1rem;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .add-to-cart-btn:hover {
            background: #D35400;
            transform: translateY(-1px);
        }

        /* Toast 通知 */
        .toast-success {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%) translateY(100px);
            background: var(--success);
            color: var(--white);
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 3000;
        }

        .toast-success.show {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }

        .cart-footer {
            background: #FAFAFA;
            padding: 2rem;
            border-top: 1px solid #E0E0E0;
        }

        .cart-total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .cart-total-label {
            font-size: 1.1rem;
            font-weight: 500;
            color: var(--primary);
            letter-spacing: 1px;
        }

        .cart-total-amount {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--accent);
        }

        .checkout-btn {
            width: 100%;
            background: var(--dark);
            color: var(--white);
            border: none;
            padding: 1.2rem;
            font-size: 1rem;
            font-weight: 500;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }

        .checkout-btn:hover:not(:disabled) {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
        }

        .checkout-btn:disabled {
            background: var(--gray);
            cursor: not-allowed;
            opacity: 0.6;
        }

        /* 訂單表單 - 極簡風格 */
        .order-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 3000;
            padding: 1rem;
            overflow-y: auto;
        }

        .order-form {
            background: var(--white);
            max-width: 600px;
            margin: 2rem auto;
            padding: 3rem;
        }

        .form-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .form-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.8rem;
            font-weight: 300;
            letter-spacing: 2px;
            color: var(--primary);
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--primary);
            letter-spacing: 0.5px;
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 1rem;
            border: 1px solid #E0E0E0;
            background: #FAFAFA;
            font-size: 1rem;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--accent);
            background: var(--white);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            margin-top: 3rem;
        }

        .form-btn {
            flex: 1;
            padding: 1rem;
            border: none;
            font-size: 1rem;
            font-weight: 500;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }

        .form-btn-submit {
            background: var(--dark);
            color: var(--white);
        }

        .form-btn-submit:hover {
            background: var(--accent);
        }

        .form-btn-cancel {
            background: transparent;
            color: var(--dark);
            border: 1px solid var(--dark);
        }

        .form-btn-cancel:hover {
            background: var(--dark);
            color: var(--white);
        }

        /* 成功訊息 - 優雅提示 */
        .success-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 4000;
            align-items: center;
            justify-content: center;
        }

        .success-box {
            background: var(--white);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            margin: 1rem;
        }

        .success-icon {
            font-size: 4rem;
            color: var(--accent);
            margin-bottom: 1rem;
        }

        .success-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.8rem;
            font-weight: 300;
            letter-spacing: 2px;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .success-message {
            color: var(--gray);
            margin-bottom: 0.5rem;
        }

        .success-order-number {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--accent);
            letter-spacing: 1px;
        }

        /* 商品區域的加入購物車按鈕 */
        .add-to-cart-section {
            position: sticky;
            bottom: 0;
            background: var(--white);
            border-top: 1px solid var(--border);
            padding: 1.5rem;
            margin-top: 2rem;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.05);
            z-index: 50;
        }

        .add-to-cart-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .selection-summary {
            flex: 1;
            min-width: 200px;
        }

        .selection-title {
            font-size: 0.875rem;
            color: var(--gray);
            margin-bottom: 0.5rem;
        }

        .selection-details {
            font-weight: 600;
            color: var(--primary);
            font-size: 1.1rem;
        }

        .selection-price {
            color: var(--accent);
            font-size: 1.3rem;
            font-weight: 700;
            margin-left: 1rem;
        }

        .main-add-to-cart-btn {
            background: var(--accent);
            color: var(--white);
            border: none;
            padding: 1rem 3rem;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
        }

        .main-add-to-cart-btn:hover:not(:disabled) {
            background: #D35400;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(230, 126, 34, 0.4);
        }

        .main-add-to-cart-btn:disabled {
            background: var(--gray);
            cursor: not-allowed;
            opacity: 0.6;
            box-shadow: none;
        }

        .main-add-to-cart-btn i {
            font-size: 1.2rem;
        }

        /* 手機版優化 - 全面升級 */
        @media (max-width: 768px) {
            /* 防止橫向滾動 */
            html, body {
                overflow-x: hidden;
                width: 100%;
            }
            
            /* 優化導航欄 */
            .navbar {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            }
            
            .nav-container {
                padding: 0.75rem 1rem;
            }

            .logo {
                font-size: 1.1rem;
                letter-spacing: 1.5px;
            }

            .logo-subtitle {
                font-size: 0.65rem;
                letter-spacing: 0.8px;
            }

            .cart-button {
                padding: 0.6rem 1rem;
                font-size: 0.85rem;
                border-radius: 20px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            
            .cart-badge {
                top: -6px;
                right: -6px;
                min-width: 18px;
                height: 18px;
                font-size: 0.7rem;
                border: 2px solid white;
            }

            /* 優化主視覺 */
            .hero {
                margin-top: 55px;
                padding: 2.5rem 1rem;
                border-radius: 0 0 20px 20px;
                background: linear-gradient(135deg, #2C3E50 0%, #34495E 100%);
            }

            .hero h1 {
                font-size: 1.75rem;
                letter-spacing: 2px;
                font-weight: 400;
            }

            .hero p {
                font-size: 0.95rem;
                opacity: 0.95;
            }
            
            /* 主要內容調整 */
            .main-container {
                padding: 2rem 0;
                padding-bottom: 100px;
            }

            /* 優化區塊標題 */
            .section {
                margin-bottom: 2.5rem;
            }
            
            .section-header {
                margin-bottom: 1.5rem;
                padding: 0 1rem;
            }

            .section-title {
                font-size: 1.4rem;
                letter-spacing: 1.5px;
            }
            
            .section-subtitle {
                font-size: 0.85rem;
            }

            /* 產品網格改為橫向滾動 */
            .product-grid {
                display: flex;
                gap: 0.75rem;
                padding: 0 1rem;
                overflow-x: auto;
                overflow-y: hidden;
                scroll-snap-type: x mandatory;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }
            
            .product-grid::-webkit-scrollbar {
                display: none;
            }

            .product-card {
                flex: 0 0 75%;
                max-width: 280px;
                scroll-snap-align: center;
                padding: 1.5rem;
                border-radius: 16px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.08);
                transition: transform 0.2s;
            }
            
            .product-card:active {
                transform: scale(0.98);
            }
            
            .product-card.selected {
                box-shadow: 0 6px 20px rgba(230, 126, 34, 0.25);
            }
            
            .product-name {
                font-size: 1.15rem;
            }
            
            .product-description {
                font-size: 0.85rem;
                line-height: 1.5;
            }
            
            .product-price {
                font-size: 1.1rem;
            }

            /* 配料分類優化 */
            .category-filters {
                display: flex;
                gap: 0.5rem;
                padding: 0 1rem;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: none;
                margin-bottom: 1.5rem;
            }
            
            .category-filters::-webkit-scrollbar {
                display: none;
            }

            .category-btn {
                flex-shrink: 0;
                padding: 0.65rem 1.5rem;
                font-size: 0.85rem;
                border-radius: 20px;
                white-space: nowrap;
            }

            /* 購物車全螢幕優化 */
            .cart-sidebar {
                width: 100%;
                right: -100%;
                top: 0;
                bottom: 0;
                box-shadow: none;
                background: #FAFAFA;
            }
            
            .cart-sidebar.open {
                right: 0;
            }

            .cart-header {
                padding: 1.25rem 1rem;
                padding-top: calc(1.25rem + env(safe-area-inset-top));
                background: var(--white);
                border-bottom: 1px solid #E0E0E0;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            }
            
            .cart-title {
                font-size: 1.2rem;
            }
            
            .cart-close {
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                background: #F5F5F5;
                color: #333;
            }

            .cart-content {
                padding: 1rem;
                background: #FAFAFA;
            }
            
            .cart-item {
                background: white;
                border-radius: 12px;
                padding: 1.25rem;
                margin-bottom: 0.75rem;
                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                border: none;
            }
            
            .cart-item-header {
                margin-bottom: 0.75rem;
            }
            
            .cart-item-name {
                font-size: 1rem;
            }
            
            .cart-item-price {
                font-size: 1rem;
            }
            
            .cart-item-details {
                font-size: 0.85rem;
                line-height: 1.5;
            }
            
            .quantity-control {
                background: #F5F5F5;
                border-radius: 25px;
                padding: 4px;
                border: none;
            }
            
            .quantity-control button {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                font-size: 1.1rem;
            }
            
            .quantity-control button:active {
                background: white;
            }
            
            .remove-btn {
                width: 36px;
                height: 36px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #FFEBEE;
                border-radius: 50%;
            }
            
            .cart-footer {
                padding: 1rem;
                padding-bottom: calc(1rem + env(safe-area-inset-bottom));
                background: white;
                box-shadow: 0 -4px 20px rgba(0,0,0,0.08);
            }
            
            .cart-total {
                margin-bottom: 1rem;
            }
            
            .cart-total-label {
                font-size: 0.9rem;
            }
            
            .cart-total-amount {
                font-size: 1.5rem;
            }
            
            .checkout-btn {
                padding: 1rem;
                font-size: 1rem;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(230, 126, 34, 0.25);
            }

            /* 訂單表單優化 */
            .order-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: white;
                z-index: 3000;
                transform: translateY(100%);
                transition: transform 0.3s ease;
                display: none;
            }
            
            .order-modal.show {
                display: block;
                transform: translateY(0);
            }

            .order-form {
                width: 100%;
                height: 100%;
                max-width: 100%;
                margin: 0;
                border-radius: 0;
                display: flex;
                flex-direction: column;
                padding: 0;
            }
            
            .form-header {
                position: sticky;
                top: 0;
                z-index: 10;
                background: white;
                padding: 1rem;
                padding-top: calc(1rem + env(safe-area-inset-top));
                border-bottom: 1px solid #E0E0E0;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                display: flex;
                align-items: center;
            }
            
            .form-title {
                font-size: 1.3rem;
                margin-left: 0.75rem;
            }
            
            /* 添加返回按鈕 */
            .form-header::before {
                content: '←';
                font-size: 1.5rem;
                margin-right: 0.75rem;
                cursor: pointer;
            }
            
            form {
                flex: 1;
                overflow-y: auto;
                padding: 1.5rem 1rem;
                padding-bottom: 100px;
            }
            
            .form-group {
                margin-bottom: 1.5rem;
            }
            
            .form-label {
                font-size: 0.9rem;
                margin-bottom: 0.5rem;
                font-weight: 600;
                color: #333;
            }
            
            .form-input,
            .form-select,
            .form-textarea {
                font-size: 16px; /* 防止 iOS 自動縮放 */
                padding: 0.875rem 1rem;
                border-radius: 12px;
                border: 1px solid #DDD;
                background: #F8F9FA;
            }
            
            .form-input:focus,
            .form-select:focus,
            .form-textarea:focus {
                outline: none;
                border-color: var(--accent);
                background: white;
                box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
            }
            
            /* 7-11 門市選擇按鈕優化 */
            .btn.btn-primary[onclick*="openSevenElevenMap"] {
                width: 100%;
                padding: 1rem;
                background: white;
                border: 1px solid #DDD;
                border-radius: 12px;
                font-size: 1rem;
                font-weight: 500;
                color: #333;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            
            .btn.btn-primary[onclick*="openSevenElevenMap"]:active {
                background: #F5F5F5;
            }
            
            .btn.btn-primary[onclick*="openSevenElevenMap"] i:first-child {
                color: var(--accent);
                font-size: 1.2rem;
            }
            
            .btn.btn-primary[onclick*="openSevenElevenMap"] i:last-child {
                color: #999;
                font-size: 0.9rem;
            }
            
            #selectedStoreInfo {
                background: #E8F4F8;
                border-radius: 12px;
                padding: 0.875rem;
                margin-top: 0.5rem;
                font-size: 0.85rem;
                line-height: 1.5;
            }

            .form-actions {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                padding: 1rem;
                padding-bottom: calc(1rem + env(safe-area-inset-bottom));
                box-shadow: 0 -4px 20px rgba(0,0,0,0.08);
                display: flex;
                gap: 0.75rem;
            }
            
            .form-btn {
                flex: 1;
                padding: 0.875rem;
                font-size: 1rem;
                border-radius: 12px;
                font-weight: 600;
            }
            
            .form-btn-cancel {
                background: #F5F5F5;
                color: #666;
            }
            
            .form-btn-submit {
                background: var(--accent);
                color: white;
                box-shadow: 0 4px 12px rgba(230, 126, 34, 0.25);
            }
        }

        @media (max-width: 480px) {
            .hero h1 {
                font-size: 1.5rem;
            }

            .product-name {
                font-size: 1.1rem;
            }

            .product-price {
                font-size: 1rem;
            }

            .cart-total-amount {
                font-size: 1.5rem;
            }
        }
        
        /* 手機版底部固定選擇欄 */
        @media (max-width: 768px) {
            .mobile-selection-bar {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                padding: 0.75rem 1rem;
                padding-bottom: calc(0.75rem + env(safe-area-inset-bottom));
                box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
                z-index: 800;
                transform: translateY(100%);
                transition: transform 0.3s ease;
            }
            
            .mobile-selection-bar.show {
                transform: translateY(0);
            }
            
            .mobile-selection-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 1rem;
            }
            
            .mobile-selection-info {
                flex: 1;
            }
            
            .mobile-selection-text {
                font-size: 0.8rem;
                color: #666;
                margin-bottom: 2px;
            }
            
            .mobile-selection-details {
                display: flex;
                align-items: baseline;
                gap: 0.5rem;
            }
            
            .mobile-selection-items {
                font-size: 0.9rem;
                font-weight: 600;
                color: #333;
            }
            
            .mobile-selection-price {
                font-size: 1.1rem;
                font-weight: 700;
                color: var(--accent);
            }
            
            .mobile-add-btn {
                background: var(--accent);
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 25px;
                font-size: 0.9rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                box-shadow: 0 2px 8px rgba(230, 126, 34, 0.25);
            }
            
            .mobile-add-btn:active:not(:disabled) {
                transform: scale(0.95);
            }
            
            .mobile-add-btn:disabled {
                background: #CCC;
                box-shadow: none;
            }
        }
        
        /* 手機版觸控優化 */
        @media (max-width: 768px) {
            /* 增大所有可點擊元素的觸控區域 */
            button, a, .clickable, input[type="radio"], input[type="checkbox"] {
                min-height: 44px;
                min-width: 44px;
            }
            
            /* 移除 hover 效果，改用 active */
            @media (hover: none) {
                .product-card:hover,
                .category-btn:hover,
                .cart-button:hover,
                button:hover {
                    transform: none;
                }
            }
            
            /* 添加觸控反饋 */
            .touchable {
                -webkit-tap-highlight-color: rgba(230, 126, 34, 0.1);
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                user-select: none;
            }
            
            /* 防止雙擊縮放 */
            * {
                touch-action: manipulation;
            }
        }
        
        /* 成功訊息優化 */
        @media (max-width: 768px) {
            .success-overlay {
                padding: 1.5rem;
            }
            
            .success-box {
                width: 100%;
                max-width: 300px;
                padding: 2rem 1.5rem;
                border-radius: 20px;
            }
            
            .success-icon {
                font-size: 3rem;
                margin-bottom: 1rem;
            }
            
            .success-title {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
            }
            
            .success-message {
                font-size: 1rem;
                margin-bottom: 1rem;
            }
            
            .success-order-number {
                font-size: 0.85rem;
                padding: 0.5rem 1rem;
                background: #F5F5F5;
                border-radius: 8px;
                font-family: monospace;
            }
        }
        
        /* 公告手機版優化 */
        @media (max-width: 768px) {
            .announcement {
                padding: 0.875rem 1rem;
                margin: 0 1rem 0.75rem;
                border-radius: 12px;
                font-size: 0.85rem;
            }
            
            .announcement-icon {
                font-size: 1.25rem;
            }
            
            .announcement-title {
                font-size: 0.95rem;
                margin-bottom: 0.25rem;
            }
            
            .announcement-text {
                font-size: 0.85rem;
                line-height: 1.4;
            }
            
            .announcement-close {
                width: 28px;
                height: 28px;
                font-size: 1rem;
                top: 0.5rem;
                right: 0.5rem;
            }
        }
        
        /* 橫向模式優化 */
        @media (max-height: 500px) and (orientation: landscape) {
            .hero {
                padding: 1.5rem 1rem;
                margin-top: 50px;
            }
            
            .hero h1 {
                font-size: 1.5rem;
            }
            
            .hero p {
                font-size: 0.85rem;
            }
            
            .section {
                margin-bottom: 1.5rem;
            }
            
            .product-card {
                padding: 1rem;
            }
            
            .product-name {
                font-size: 1rem;
            }
            
            .cart-content {
                height: calc(100vh - 140px);
            }
        }
        
        /* 深色模式支援 */
        @media (prefers-color-scheme: dark) and (max-width: 768px) {
            body {
                background: #1A1A1A;
                color: #E0E0E0;
            }
            
            .navbar {
                background: rgba(42, 42, 42, 0.98);
            }
            
            .hero {
                background: linear-gradient(135deg, #1A1A1A 0%, #2A2A2A 100%);
            }
            
            .product-card,
            .cart-item,
            .form-input,
            .form-select,
            .form-textarea {
                background: #2A2A2A;
                color: #E0E0E0;
                border-color: #444;
            }
            
            .product-card.selected {
                background: #333;
            }
            
            .cart-sidebar {
                background: #1A1A1A;
            }
            
            .cart-header,
            .cart-footer,
            .form-header,
            .form-actions,
            .mobile-selection-bar {
                background: #2A2A2A;
            }
        }

        /* 載入動畫 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .loading-spinner {
            border: 2px solid var(--light);
            border-top: 2px solid var(--accent);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 浮動結帳按鈕 */
        .floating-checkout {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: var(--accent);
            color: var(--white);
            padding: 1rem 2rem;
            border-radius: 50px;
            box-shadow: 0 4px 20px rgba(230, 126, 34, 0.4);
            cursor: pointer;
            display: none;
            align-items: center;
            gap: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
            z-index: 900;
            border: none;
            font-size: 1rem;
        }

        .floating-checkout.show {
            display: flex;
        }

        .floating-checkout:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 30px rgba(230, 126, 34, 0.5);
            background: #D35400;
        }

        .floating-checkout-badge {
            background: var(--white);
            color: var(--accent);
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 700;
        }

        /* 捲軸樣式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gray);
            transition: background 0.3s;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--dark);
        }

        /* 浮動結帳按鈕 */
        .floating-checkout {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(26, 26, 26, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: var(--white);
            padding: 1rem 2.5rem;
            border-radius: 50px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 900;
            display: none;
            align-items: center;
            gap: 1rem;
            font-weight: 500;
            font-size: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .floating-checkout.show {
            display: flex;
        }

        .floating-checkout:hover {
            background: rgba(230, 126, 34, 0.95);
            transform: translateX(-50%) translateY(-3px);
            box-shadow: 0 12px 40px rgba(230, 126, 34, 0.4);
        }

        /* 浮動按鈕動畫 */
        @keyframes pulse {
            0% {
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            }
            50% {
                box-shadow: 0 8px 32px rgba(230, 126, 34, 0.4);
            }
            100% {
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            }
        }

        .floating-checkout.pulse {
            animation: pulse 2s ease-in-out infinite;
        }

        .floating-checkout-badge {
            background: var(--accent);
            color: var(--white);
            min-width: 24px;
            height: 24px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.85rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .floating-checkout-price {
            font-weight: 700;
            font-size: 1.1rem;
            letter-spacing: 0.5px;
        }

        /* 手機版調整 */
        @media (max-width: 768px) {
            .floating-checkout {
                bottom: 1rem;
                left: 1rem;
                right: 1rem;
                transform: none;
                width: auto;
                padding: 0.875rem 1.5rem;
                font-size: 0.95rem;
            }

            .floating-checkout:hover {
                transform: translateY(-2px);
            }

            .main-content {
                padding-bottom: 5rem;
            }

            .add-to-cart-section {
                padding: 1rem;
            }

            .add-to-cart-container {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .main-add-to-cart-btn {
                width: 100%;
                justify-content: center;
                padding: 1rem 2rem;
            }
        }
        
        /* 公告樣式 */
        .announcements-container {
            max-width: 1200px;
            margin: 0 auto 2rem;
            padding: 0 1rem;
        }

        .announcement {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideOut {
            to {
                opacity: 0;
                transform: translateX(20px);
            }
        }

        .announcement-icon {
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .announcement-body {
            flex: 1;
        }

        .announcement-title {
            margin: 0 0 0.5rem;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .announcement-text {
            margin: 0;
            line-height: 1.5;
            color: #666;
        }

        .announcement-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #999;
            cursor: pointer;
            transition: color 0.2s;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .announcement-close:hover {
            color: #333;
        }

        /* 公告類型樣式 */
        .announcement-info {
            background: #E3F2FD;
            border-left: 4px solid #2196F3;
        }

        .announcement-info .announcement-icon {
            color: #2196F3;
        }

        .announcement-warning {
            background: #FFF8E1;
            border-left: 4px solid #FFC107;
        }

        .announcement-warning .announcement-icon {
            color: #FFA000;
        }

        .announcement-success {
            background: #E8F5E9;
            border-left: 4px solid #4CAF50;
        }

        .announcement-success .announcement-icon {
            color: #4CAF50;
        }

        .announcement-promotion {
            background: #FCE4EC;
            border-left: 4px solid #E91E63;
        }

        .announcement-promotion .announcement-icon {
            color: #E91E63;
        }

        /* 成功訊息 Toast */
        .toast-success {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%) translateY(100px);
            background: #4CAF50;
            color: white;
            padding: 1rem 2rem;
            border-radius: 30px;
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 10000;
        }

        .toast-success.show {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }

        /* 手機版公告調整 */
        @media (max-width: 768px) {
            .announcement {
                padding: 1rem;
                font-size: 0.9rem;
            }

            .announcement-icon {
                font-size: 1.2rem;
            }

            .announcement-title {
                font-size: 1rem;
            }

            .announcement-close {
                top: 0.5rem;
                right: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 導航列 -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="logo">
                <span>海水不可斗量</span>
                <span class="logo-subtitle">THE SEA IS IMMEASURABLE</span>
            </a>
            <button class="cart-button" onclick="toggleCart()">
                購物車
                <span class="cart-badge" id="cartCount">0</span>
            </button>
        </div>
    </nav>

    <!-- 主視覺 -->
    <section class="hero">
        <div class="hero-content">
            <h1>義式手工冰淇淋</h1>
            <p>精選食材 · 匠心製作 · 獨特風味</p>
        </div>
    </section>

    <!-- 主要內容 -->
    <main class="main-container">
        <!-- 公告區域 -->
        <div id="announcementsContainer" class="announcements-container" style="display: none;">
            <!-- 公告將動態載入到這裡 -->
        </div>
        
        <!-- 基底選擇 -->
        <section class="section">
            <div class="section-header">
                <h2 class="section-title">選擇基底</h2>
                <p class="section-subtitle">SELECT YOUR BASE</p>
            </div>
            <div class="product-grid" id="baseGrid">
                <div class="loading">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </section>

        <!-- 配料選擇 -->
        <section class="section">
            <div class="section-header">
                <h2 class="section-title">精選配料</h2>
                <p class="section-subtitle">PREMIUM TOPPINGS</p>
            </div>
            <div class="category-filters">
                <button class="category-btn active" onclick="filterToppings('all')">全部</button>
                <button class="category-btn" onclick="filterToppings('糖漿')">糖漿</button>
                <button class="category-btn" onclick="filterToppings('水果')">水果</button>
                <button class="category-btn" onclick="filterToppings('堅果')">堅果</button>
                <button class="category-btn" onclick="filterToppings('糖果')">糖果</button>
                <button class="category-btn" onclick="filterToppings('其他')">其他</button>
            </div>
            <div class="product-grid" id="toppingGrid">
                <div class="loading">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </section>

        <!-- 加入購物車區域 -->
        <div class="add-to-cart-section" id="addToCartSection" style="display: none;">
            <div class="add-to-cart-container">
                <div class="selection-summary">
                    <div class="selection-title">您的選擇</div>
                    <div class="selection-details">
                        <span id="selectionText">請選擇基底口味</span>
                        <span class="selection-price" id="selectionPrice">NT$ 0</span>
                    </div>
                </div>
                <button class="main-add-to-cart-btn" id="mainAddToCartBtn" onclick="addToCart()" disabled>
                    <i class="fas fa-cart-plus"></i>
                    加入購物車
                </button>
            </div>
        </div>
    </main>

    <!-- 購物車側邊欄 -->
    <aside class="cart-sidebar" id="cartSidebar">
        <div class="cart-header">
            <h3 class="cart-title">您的選擇</h3>
            <button class="cart-close" onclick="toggleCart()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="cart-content" id="cartContent">
            <div class="cart-empty">
                <p>請選擇您喜愛的口味</p>
            </div>
        </div>
        <div class="cart-footer">
            <div class="cart-total">
                <span class="cart-total-label">總計</span>
                <span class="cart-total-amount" id="totalPrice">NT$ 0</span>
            </div>
            <button class="checkout-btn" onclick="showOrderForm()" id="checkoutButton">
                <i class="fas fa-credit-card"></i> 前往結帳
            </button>
        </div>
    </aside>

    <!-- 訂單表單 -->
    <div class="order-modal" id="orderModal">
        <div class="order-form">
            <div class="form-header">
                <h2 class="form-title">訂購資訊</h2>
            </div>
            <form id="checkoutForm">
                <div class="form-group">
                    <label class="form-label">姓名</label>
                    <input type="text" class="form-input" id="customerName" required>
                </div>
                <div class="form-group">
                    <label class="form-label">聯絡電話</label>
                    <input type="tel" class="form-input" id="customerPhone" required>
                </div>
                <div class="form-group">
                    <label class="form-label">電子郵件</label>
                    <input type="email" class="form-input" id="customerEmail" required>
                </div>
                <div class="form-group">
                    <label class="form-label">7-11 取貨門市</label>
                    <button type="button" class="btn btn-primary" style="width: 100%; margin-bottom: 0.5rem;" onclick="openSevenElevenMap()">
                        <i class="fas fa-map-marker-alt"></i> 選擇 7-11 門市
                    </button>
                    <input type="hidden" id="storeId" required>
                    <input type="hidden" id="storeName">
                    <input type="hidden" id="storeAddress">
                    <input type="hidden" id="storeTelephone">
                    <div id="selectedStoreInfo" style="margin-top: 0.5rem; padding: 0.75rem; background: #F5F5F5; border-radius: 5px; display: none;">
                        <small style="color: #666;"></small>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">付款方式</label>
                    <select class="form-select" id="paymentMethod">
                        <option value="cash">7-11 取貨付款</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">備註</label>
                    <textarea class="form-textarea" id="orderNotes" placeholder="特殊需求或配送說明"></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="form-btn form-btn-cancel" onclick="hideOrderForm()">取消</button>
                    <button type="submit" class="form-btn form-btn-submit">確認訂購</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 成功訊息 -->
    <div class="success-overlay" id="successOverlay">
        <div class="success-box">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h2 class="success-title">訂購成功</h2>
            <p class="success-message">感謝您的訂購，我們將盡快為您準備</p>
            <p class="success-order-number">訂單編號：<span id="orderNumber"></span></p>
        </div>
    </div>

    <!-- 浮動結帳按鈕 -->
    <div class="floating-checkout" id="floatingCheckout" onclick="toggleCart()">
        <i class="fas fa-shopping-cart"></i>
        <span>查看購物車</span>
        <span class="floating-checkout-badge" id="floatingBadge">0</span>
        <span class="floating-checkout-price" id="floatingPrice">NT$ 0</span>
    </div>
    
    <!-- 手機版底部選擇欄 -->
    <div class="mobile-selection-bar" id="mobileSelectionBar">
        <div class="mobile-selection-content">
            <div class="mobile-selection-info">
                <div class="mobile-selection-text">目前選擇</div>
                <div class="mobile-selection-details">
                    <span class="mobile-selection-items" id="mobileSelectionItems">請選擇基底口味</span>
                    <span class="mobile-selection-price" id="mobileSelectionPrice">NT$ 0</span>
                </div>
            </div>
            <button class="mobile-add-btn touchable" id="mobileAddBtn" onclick="addToCart()" disabled>
                <i class="fas fa-plus"></i>
                加入
            </button>
        </div>
    </div>

    <script>
        // API 設定
        const API_BASE = 'http://localhost:3000/api';
        
        // 狀態管理
        let products = { bases: [], toppings: [] };
        let selectedBase = null;
        let selectedToppings = [];
        let currentCategory = 'all';

        // 初始化
        window.onload = async function() {
            await loadProducts();
            await loadAnnouncements();
            updateCartBadge();
            initMobileOptimizations();
        };
        
        // 初始化手機版優化
        function initMobileOptimizations() {
            // 檢測是否為手機設備
            const isMobile = window.innerWidth <= 768;
            
            if (isMobile) {
                // 添加手機版樣式類
                document.body.classList.add('mobile-device');
                
                // 處理橫向滾動的觸控事件
                const productGrids = document.querySelectorAll('.product-grid');
                productGrids.forEach(grid => {
                    let startX, scrollLeft;
                    
                    grid.addEventListener('touchstart', (e) => {
                        startX = e.touches[0].pageX - grid.offsetLeft;
                        scrollLeft = grid.scrollLeft;
                    });
                    
                    grid.addEventListener('touchmove', (e) => {
                        if (!startX) return;
                        e.preventDefault();
                        const x = e.touches[0].pageX - grid.offsetLeft;
                        const walk = (x - startX) * 2;
                        grid.scrollLeft = scrollLeft - walk;
                    });
                });
                
                // 處理購物車滑動關閉
                let touchStartX = 0;
                const cartSidebar = document.querySelector('.cart-sidebar');
                
                cartSidebar.addEventListener('touchstart', (e) => {
                    touchStartX = e.touches[0].clientX;
                });
                
                cartSidebar.addEventListener('touchend', (e) => {
                    const touchEndX = e.changedTouches[0].clientX;
                    const diffX = touchEndX - touchStartX;
                    
                    // 右滑關閉購物車
                    if (diffX > 100) {
                        toggleCart();
                    }
                });
                
                // 優化表單輸入體驗
                const formInputs = document.querySelectorAll('.form-input, .form-textarea');
                formInputs.forEach(input => {
                    input.addEventListener('focus', () => {
                        // 滾動到輸入框位置
                        setTimeout(() => {
                            input.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 300);
                    });
                });
            }
            
            // 監聽視窗大小變化
            let resizeTimer;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(() => {
                    updateSelectionSummary();
                }, 250);
            });
        }

        // 載入產品
        async function loadProducts() {
            try {
                const response = await fetch(`${API_BASE}/products`);
                if (response.ok) {
                    const allProducts = await response.json();
                    products.bases = allProducts.filter(p => p.type === 'base');
                    products.toppings = allProducts.filter(p => p.type === 'topping');
                } else {
                    throw new Error('API not available');
                }
            } catch (error) {
                console.log('Using mock data:', error);
                await initializeMockData();
            }
            
            renderBases();
            renderToppings();
        }
        
        // 載入公告
        async function loadAnnouncements() {
            try {
                const response = await fetch(`${API_BASE}/announcements/active`);
                if (response.ok) {
                    const announcements = await response.json();
                    displayAnnouncements(announcements);
                }
            } catch (error) {
                console.error('載入公告失敗:', error);
            }
        }
        
        // 顯示公告
        function displayAnnouncements(announcements) {
            const container = document.getElementById('announcementsContainer');
            
            if (announcements.length === 0) {
                container.style.display = 'none';
                return;
            }
            
            container.style.display = 'block';
            container.innerHTML = announcements.map(announcement => {
                const typeClass = getAnnouncementTypeClass(announcement.type);
                return `
                    <div class="announcement ${typeClass}">
                        <div class="announcement-icon">
                            ${getAnnouncementIcon(announcement.type)}
                        </div>
                        <div class="announcement-body">
                            <h4 class="announcement-title">${announcement.title}</h4>
                            <p class="announcement-text">${announcement.content}</p>
                        </div>
                        <button class="announcement-close" onclick="closeAnnouncement(this)">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
            }).join('');
        }
        
        // 獲取公告類型樣式
        function getAnnouncementTypeClass(type) {
            const classMap = {
                'info': 'announcement-info',
                'warning': 'announcement-warning',
                'success': 'announcement-success',
                'promotion': 'announcement-promotion'
            };
            return classMap[type] || 'announcement-info';
        }
        
        // 獲取公告圖標
        function getAnnouncementIcon(type) {
            const iconMap = {
                'info': '<i class="fas fa-info-circle"></i>',
                'warning': '<i class="fas fa-exclamation-triangle"></i>',
                'success': '<i class="fas fa-check-circle"></i>',
                'promotion': '<i class="fas fa-gift"></i>'
            };
            return iconMap[type] || iconMap.info;
        }
        
        // 關閉單個公告
        window.closeAnnouncement = function(button) {
            const announcement = button.closest('.announcement');
            announcement.style.animation = 'slideOut 0.3s ease forwards';
            setTimeout(() => {
                announcement.remove();
                // 如果沒有公告了，隱藏容器
                const container = document.getElementById('announcementsContainer');
                if (container.children.length === 0) {
                    container.style.display = 'none';
                }
            }, 300);
        };

        // 初始化模擬資料
        async function initializeMockData() {
            try {
                await fetch(`${API_BASE}/products/init-samples`, { method: 'POST' });
                await loadProducts();
            } catch (error) {
                // 使用本地模擬資料
                products.bases = [
                    { _id: '1', name: '經典香草', price: 80, description: '馬達加斯加香草莢' },
                    { _id: '2', name: '比利時巧克力', price: 85, description: '70%可可純度' },
                    { _id: '3', name: '新鮮草莓', price: 85, description: '當季新鮮草莓' },
                    { _id: '4', name: '宇治抹茶', price: 90, description: '日本進口抹茶粉' },
                    { _id: '5', name: '愛文芒果', price: 90, description: '台灣在地芒果' },
                    { _id: '6', name: '薄荷巧克力', price: 95, description: '清新薄荷與巧克力' },
                    { _id: '7', name: '焦糖海鹽', price: 95, description: '手工焦糖與海鹽' },
                    { _id: '8', name: '金枕頭榴槤', price: 100, description: '泰國頂級榴槤' },
                    { _id: '9', name: '大甲芋頭', price: 85, description: '台灣特選芋頭' },
                    { _id: '10', name: '西西里檸檬', price: 80, description: '義大利檸檬雪酪' }
                ];

                products.toppings = [
                    // 糖漿類 (NT$ 15-20)
                    { _id: '11', name: '比利時巧克力醬', category: '糖漿', price: 15, description: '濃郁可可風味' },
                    { _id: '12', name: '手工焦糖醬', category: '糖漿', price: 15, description: '香甜焦糖' },
                    { _id: '13', name: '覆盆子醬', category: '糖漿', price: 18, description: '酸甜莓果' },
                    { _id: '14', name: '楓糖漿', category: '糖漿', price: 20, description: '加拿大進口' },
                    { _id: '15', name: '蜂蜜', category: '糖漿', price: 15, description: '天然蜂蜜' },
                    
                    // 水果類 (NT$ 20-35)
                    { _id: '16', name: '野生藍莓', category: '水果', price: 30, description: '新鮮藍莓' },
                    { _id: '17', name: '新鮮草莓切片', category: '水果', price: 25, description: '當季草莓' },
                    { _id: '18', name: '芒果丁', category: '水果', price: 25, description: '愛文芒果' },
                    { _id: '19', name: '奇異果片', category: '水果', price: 20, description: '紐西蘭奇異果' },
                    { _id: '20', name: '鳳梨塊', category: '水果', price: 20, description: '金鑽鳳梨' },
                    { _id: '21', name: '櫻桃', category: '水果', price: 35, description: '進口櫻桃' },
                    
                    // 堅果類 (NT$ 20-35)
                    { _id: '22', name: '夏威夷果仁', category: '堅果', price: 35, description: '烘焙夏威夷果' },
                    { _id: '23', name: '烘焙杏仁片', category: '堅果', price: 20, description: '香脆杏仁' },
                    { _id: '24', name: '核桃碎', category: '堅果', price: 25, description: '加州核桃' },
                    { _id: '25', name: '開心果', category: '堅果', price: 30, description: '土耳其開心果' },
                    { _id: '26', name: '腰果', category: '堅果', price: 25, description: '越南腰果' },
                    { _id: '27', name: '花生碎', category: '堅果', price: 20, description: '台灣花生' },
                    
                    // 糖果類 (NT$ 15-25)
                    { _id: '28', name: '手工餅乾碎', category: '糖果', price: 20, description: '酥脆餅乾' },
                    { _id: '29', name: 'Oreo巧克力餅', category: '糖果', price: 20, description: '經典Oreo' },
                    { _id: '30', name: '彩虹軟糖', category: '糖果', price: 15, description: '繽紛軟糖' },
                    { _id: '31', name: '棉花糖', category: '糖果', price: 15, description: '迷你棉花糖' },
                    { _id: '32', name: 'M&M巧克力', category: '糖果', price: 25, description: '彩色巧克力豆' },
                    { _id: '33', name: '跳跳糖', category: '糖果', price: 18, description: '趣味跳跳糖' },
                    
                    // 其他類 (NT$ 15-30)
                    { _id: '34', name: '法式鮮奶油', category: '其他', price: 15, description: '濃郁奶香' },
                    { _id: '35', name: '煉乳', category: '其他', price: 15, description: '香甜煉乳' },
                    { _id: '36', name: '抹茶粉', category: '其他', price: 25, description: '日本抹茶' },
                    { _id: '37', name: '可可粉', category: '其他', price: 20, description: '無糖可可' },
                    { _id: '38', name: '椰子片', category: '其他', price: 20, description: '烘焙椰片' },
                    { _id: '39', name: '爆米花', category: '其他', price: 20, description: '焦糖爆米花' },
                    { _id: '40', name: '麻糬', category: '其他', price: 30, description: 'Q彈麻糬' }
                ];
            }
        }

        // 渲染基底
        function renderBases() {
            const baseGrid = document.getElementById('baseGrid');
            baseGrid.innerHTML = products.bases.map(base => `
                <div class="product-card ${selectedBase?._id === base._id ? 'selected' : ''}" 
                     onclick="selectBase('${base._id}')">
                    <h3 class="product-name">${base.name}</h3>
                    <p class="product-description">${base.description || ''}</p>
                    <div class="product-price">NT$ ${base.price}</div>
                </div>
            `).join('');
        }

        // 渲染配料
        function renderToppings() {
            const toppingGrid = document.getElementById('toppingGrid');
            const filtered = currentCategory === 'all' 
                ? products.toppings 
                : products.toppings.filter(t => t.category === currentCategory);
            
            toppingGrid.innerHTML = filtered.map(topping => {
                const isSelected = selectedToppings.some(t => t._id === topping._id);
                return `
                    <div class="product-card ${isSelected ? 'selected' : ''}" 
                         onclick="toggleTopping('${topping._id}')">
                        <h3 class="product-name">${topping.name}</h3>
                        <div class="product-price">+NT$ ${topping.price}</div>
                    </div>
                `;
            }).join('');
        }

        // 選擇基底
        function selectBase(baseId) {
            selectedBase = products.bases.find(b => b._id === baseId);
            renderBases();
            updateCart();
            updateSelectionSummary();
        }

        // 切換配料
        function toggleTopping(toppingId) {
            const topping = products.toppings.find(t => t._id === toppingId);
            const index = selectedToppings.findIndex(t => t._id === toppingId);
            
            if (index > -1) {
                selectedToppings.splice(index, 1);
            } else {
                selectedToppings.push(topping);
            }
            
            renderToppings();
            updateCart();
            updateSelectionSummary();
        }

        // 篩選配料
        function filterToppings(category) {
            currentCategory = category;
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.toggle('active', 
                    btn.textContent === (category === 'all' ? '全部' : category));
            });
            renderToppings();
        }

        // 購物車項目
        let cartItems = [];

        // 新增到購物車
        function addToCart() {
            if (!selectedBase) {
                alert('請先選擇基底口味');
                return;
            }

            const item = {
                id: Date.now(),
                base: selectedBase,
                toppings: [...selectedToppings],
                quantity: 1,
                price: selectedBase.price + selectedToppings.reduce((sum, t) => sum + t.price, 0)
            };

            cartItems.push(item);
            
            // 重置選擇
            selectedBase = null;
            selectedToppings = [];
            renderBases();
            renderToppings();
            
            // 更新購物車和選擇摘要
            updateCart();
            updateSelectionSummary();
            
            // 顯示成功訊息
            showAddToCartSuccess();
            
            // 添加脈衝動畫提示
            const floatingCheckout = document.getElementById('floatingCheckout');
            floatingCheckout.classList.add('pulse');
            setTimeout(() => {
                floatingCheckout.classList.remove('pulse');
            }, 3000);
        }

        // 顯示加入購物車成功訊息
        function showAddToCartSuccess() {
            const toast = document.createElement('div');
            toast.className = 'toast-success';
            toast.innerHTML = '<i class="fas fa-check-circle"></i> 已加入購物車';
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);
            
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }

        // 更新購物車
        function updateCart() {
            const cartContent = document.getElementById('cartContent');
            const totalPrice = document.getElementById('totalPrice');
            
            if (cartItems.length === 0 && !selectedBase) {
                cartContent.innerHTML = '<div class="cart-empty"><p>請選擇您喜愛的口味</p></div>';
                totalPrice.textContent = 'NT$ 0';
                updateCartBadge();
                return;
            }
            
            let cartHTML = '';
            let total = 0;

            // 顯示已加入購物車的項目
            cartItems.forEach((item, index) => {
                total += item.price * item.quantity;
                cartHTML += `
                    <div class="cart-item">
                        <div class="cart-item-header">
                            <span class="cart-item-name">冰淇淋 #${index + 1}</span>
                            <span class="cart-item-price">NT$ ${item.price}</span>
                        </div>
                        <div class="cart-item-details">
                            基底：${item.base.name}
                            ${item.toppings.length > 0 ? `<br>配料：${item.toppings.map(t => t.name).join('、')}` : ''}
                        </div>
                        <div class="cart-item-actions">
                            <div class="quantity-control">
                                <button onclick="updateQuantity(${item.id}, -1)">-</button>
                                <span>${item.quantity}</span>
                                <button onclick="updateQuantity(${item.id}, 1)">+</button>
                            </div>
                            <button class="remove-btn" onclick="removeFromCart(${item.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            });

            // 顯示當前選擇（尚未加入購物車）
            if (selectedBase) {
                const currentPrice = selectedBase.price + selectedToppings.reduce((sum, t) => sum + t.price, 0);
                cartHTML += `
                    <div class="cart-item current-selection">
                        <div class="cart-item-header">
                            <span class="cart-item-name">當前選擇</span>
                            <span class="cart-item-price">NT$ ${currentPrice}</span>
                        </div>
                        <div class="cart-item-details">
                            基底：${selectedBase.name}
                            ${selectedToppings.length > 0 ? `<br>配料：${selectedToppings.map(t => t.name).join('、')}` : ''}
                        </div>
                        <button class="add-to-cart-btn" onclick="addToCart()">
                            <i class="fas fa-plus"></i> 加入購物車
                        </button>
                    </div>
                `;
            }
            
            cartContent.innerHTML = cartHTML;
            totalPrice.textContent = `NT$ ${total}`;
            updateCartBadge();
            
            // 更新結帳按鈕狀態
            const checkoutBtn = document.getElementById('checkoutButton');
            if (cartItems.length === 0) {
                checkoutBtn.disabled = true;
                checkoutBtn.innerHTML = '<i class="fas fa-shopping-cart"></i> 購物車是空的';
            } else {
                checkoutBtn.disabled = false;
                checkoutBtn.innerHTML = '<i class="fas fa-credit-card"></i> 前往結帳';
            }
        }

        // 更新數量
        function updateQuantity(itemId, change) {
            const item = cartItems.find(i => i.id === itemId);
            if (item) {
                item.quantity += change;
                if (item.quantity <= 0) {
                    removeFromCart(itemId);
                } else {
                    updateCart();
                }
            }
        }

        // 從購物車移除
        function removeFromCart(itemId) {
            cartItems = cartItems.filter(i => i.id !== itemId);
            updateCart();
        }

        // 更新選擇摘要
        function updateSelectionSummary() {
            const addToCartSection = document.getElementById('addToCartSection');
            const selectionText = document.getElementById('selectionText');
            const selectionPrice = document.getElementById('selectionPrice');
            const mainAddToCartBtn = document.getElementById('mainAddToCartBtn');
            
            // 手機版元素
            const mobileSelectionBar = document.getElementById('mobileSelectionBar');
            const mobileSelectionItems = document.getElementById('mobileSelectionItems');
            const mobileSelectionPrice = document.getElementById('mobileSelectionPrice');
            const mobileAddBtn = document.getElementById('mobileAddBtn');
            
            if (selectedBase) {
                addToCartSection.style.display = 'block';
                
                let text = selectedBase.name;
                if (selectedToppings.length > 0) {
                    text += ` + ${selectedToppings.length} 種配料`;
                }
                
                const price = selectedBase.price + selectedToppings.reduce((sum, t) => sum + t.price, 0);
                
                // 更新桌面版
                selectionText.textContent = text;
                selectionPrice.textContent = `NT$ ${price}`;
                mainAddToCartBtn.disabled = false;
                
                // 更新手機版
                if (window.innerWidth <= 768) {
                    mobileSelectionBar.classList.add('show');
                    mobileSelectionItems.textContent = text;
                    mobileSelectionPrice.textContent = `NT$ ${price}`;
                    mobileAddBtn.disabled = false;
                }
            } else {
                addToCartSection.style.display = 'none';
                selectionText.textContent = '請選擇基底口味';
                selectionPrice.textContent = 'NT$ 0';
                mainAddToCartBtn.disabled = true;
                
                // 更新手機版
                if (window.innerWidth <= 768) {
                    mobileSelectionBar.classList.remove('show');
                    mobileSelectionItems.textContent = '請選擇基底口味';
                    mobileSelectionPrice.textContent = 'NT$ 0';
                    mobileAddBtn.disabled = true;
                }
            }
        }

        // 更新購物車數量
        function updateCartBadge() {
            const count = cartItems.reduce((sum, item) => sum + item.quantity, 0);
            const total = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            
            // 更新頂部購物車數量
            document.getElementById('cartCount').textContent = count;
            
            // 更新浮動按鈕
            const floatingCheckout = document.getElementById('floatingCheckout');
            const floatingBadge = document.getElementById('floatingBadge');
            const floatingPrice = document.getElementById('floatingPrice');
            
            if (count > 0) {
                floatingCheckout.classList.add('show');
                floatingBadge.textContent = count;
                floatingPrice.textContent = `NT$ ${total}`;
            } else {
                floatingCheckout.classList.remove('show');
            }
        }

        // 切換購物車
        function toggleCart() {
            const cartSidebar = document.getElementById('cartSidebar');
            const floatingCheckout = document.getElementById('floatingCheckout');
            
            cartSidebar.classList.toggle('open');
            
            // 移除脈衝動畫
            if (cartSidebar.classList.contains('open')) {
                floatingCheckout.classList.remove('pulse');
            }
        }

        // 顯示訂單表單
        function showOrderForm() {
            if (cartItems.length === 0) {
                alert('購物車是空的，請先選擇商品');
                return;
            }
            
            const orderModal = document.getElementById('orderModal');
            const isMobile = window.innerWidth <= 768;
            
            orderModal.style.display = 'block';
            
            // 手機版動畫效果
            if (isMobile) {
                document.body.style.overflow = 'hidden';
                document.body.style.position = 'fixed';
                document.body.style.width = '100%';
                
                setTimeout(() => {
                    orderModal.classList.add('show');
                }, 10);
                
                // 關閉購物車
                document.getElementById('cartSidebar').classList.remove('open');
            } else {
                document.body.style.overflow = 'hidden';
            }
            
            // 載入 7-11 門市資料
            loadSevenElevenData();
        }

        // 隱藏訂單表單
        function hideOrderForm() {
            const orderModal = document.getElementById('orderModal');
            const isMobile = window.innerWidth <= 768;
            
            if (isMobile) {
                orderModal.classList.remove('show');
                
                setTimeout(() => {
                    orderModal.style.display = 'none';
                    document.body.style.overflow = '';
                    document.body.style.position = '';
                    document.body.style.width = '';
                }, 300);
            } else {
                orderModal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }
        
        // 載入 7-11 門市資料（使用真實 API）
        function loadSevenElevenData() {
            // 真實 API 使用按鈕選擇，不需要載入資料
        }
        
        // 開啟 7-11 門市選擇地圖
        window.openSevenElevenMap = function() {
            // 獲取 7-11 地圖 URL
            const mapUrl = `https://emap.presco.com.tw/c2cemap.ashx?eshopid=870&servicetype=1&url=${encodeURIComponent(window.location.origin + '/api/seven-eleven/callback')}`;
            
            // 開啟新視窗
            const mapWindow = window.open(mapUrl, 'SevenElevenMap', 'width=800,height=600,toolbar=no,location=no,status=no,menubar=no');
            
            // 監聽來自子視窗的訊息
            window.addEventListener('message', function(event) {
                if (event.data && event.data.type === 'seven-eleven-selected') {
                    const data = event.data.data;
                    
                    // 更新隱藏欄位
                    document.getElementById('storeId').value = data.storeId;
                    document.getElementById('storeName').value = data.storeName;
                    document.getElementById('storeAddress').value = data.address;
                    document.getElementById('storeTelephone').value = data.telephone;
                    
                    // 更新顯示資訊
                    const storeInfo = document.getElementById('selectedStoreInfo');
                    storeInfo.innerHTML = `
                        <strong style="color: #2196F3;">已選擇門市：</strong><br>
                        <small style="color: #666;">
                            門市名稱：${data.storeName}<br>
                            門市代號：${data.storeId}<br>
                            地址：${data.address}<br>
                            電話：${data.telephone || '未提供'}
                        </small>
                    `;
                    storeInfo.style.display = 'block';
                }
            }, { once: false });
        }
        
        // 防抖函數
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 提交訂單
        document.getElementById('checkoutForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const storeId = document.getElementById('storeId').value;
            if (!storeId) {
                alert('請選擇 7-11 取貨門市');
                return;
            }
            
            const orderData = {
                customer_name: document.getElementById('customerName').value,
                customer_phone: document.getElementById('customerPhone').value,
                customer_email: document.getElementById('customerEmail').value,
                seven_store_id: storeId,
                seven_store_name: document.getElementById('storeName').value,
                items: cartItems.map(item => ({
                    baseId: item.base._id || item.base.id,
                    toppingIds: item.toppings.map(t => t._id || t.id),
                    quantity: item.quantity
                })),
                notes: document.getElementById('orderNotes').value
            };
            
            try {
                const response = await fetch(`${API_BASE}/orders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(orderData)
                });
                
                if (response.ok) {
                    const order = await response.json();
                    showSuccess(order.orderNumber);
                } else {
                    throw new Error('Order failed');
                }
            } catch (error) {
                console.error('Order error:', error);
                // 模擬成功
                showSuccess(`ORD${Date.now()}`);
            }
        });

        // 顯示成功訊息
        function showSuccess(orderNumber) {
            document.getElementById('orderNumber').textContent = orderNumber;
            document.getElementById('orderModal').style.display = 'none';
            document.getElementById('successOverlay').style.display = 'flex';
            
            // 重置
            setTimeout(() => {
                document.getElementById('successOverlay').style.display = 'none';
                document.getElementById('checkoutForm').reset();
                cartItems = [];
                selectedBase = null;
                selectedToppings = [];
                renderBases();
                renderToppings();
                updateCart();
                toggleCart();
                document.body.style.overflow = 'auto';
            }, 3000);
        }

        // 點擊模態框外部關閉
        document.getElementById('orderModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideOrderForm();
            }
        });

        document.getElementById('successOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        });
    </script>
</body>
</html> 