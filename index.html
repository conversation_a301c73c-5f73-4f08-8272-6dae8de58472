<!doctype html>
<html lang="zh-TW">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>DeepVape 電子煙商城 - 專業電子煙線上購物平台</title>
  
  <!-- SEO Meta Tags -->
  <meta name="description" content="DeepVape 是台灣專業的電子煙線上商城，提供各大品牌電子煙主機、煙彈、拋棄式電子煙。正品保證，快速配送，優質售後服務。" />
  <meta name="keywords" content="電子煙,電子煙主機,煙彈,拋棄式電子煙,IQOS,JUUL,Vaporesso,SP2,Ilia,HTA,Lana" />
  <meta name="author" content="DeepVape" />
  <meta name="robots" content="index, follow" />
  
  <!-- Open Graph Tags -->
  <meta property="og:title" content="DeepVape 電子煙商城 - 專業電子煙線上購物平台" />
  <meta property="og:description" content="提供各大品牌電子煙產品，正品保證，快速配送，優質售後服務。" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://deepvape.org" />
  <meta property="og:image" content="https://deepvape.org/images/itay-kabalo-b3sel60dv8a-unsplash.jpg" />
  <meta property="og:site_name" content="DeepVape" />
  <meta property="og:locale" content="zh_TW" />

  <!-- Twitter Card Tags -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="DeepVape 電子煙商城" />
  <meta name="twitter:description" content="專業電子煙線上購物平台，提供各大品牌電子煙產品。" />
  <meta name="twitter:image" content="https://deepvape.org/images/itay-kabalo-b3sel60dv8a-unsplash.jpg" />

  <!-- Canonical URL -->
  <link rel="canonical" href="https://deepvape.org" />
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  
  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Store",
    "name": "DeepVape",
    "description": "專業的電子煙線上商城",
    "url": "https://deepvape.org",
    "logo": "https://deepvape.org/vite.svg",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "TW"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": "Chinese"
    },
    "paymentAccepted": "Cash",
    "currenciesAccepted": "TWD",
    "priceRange": "$$"
  }
  </script>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>