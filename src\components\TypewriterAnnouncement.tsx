import React, { useState, useEffect } from 'react';
import { announcementsAPI } from '@/lib/api';

interface Announcement {
  id: number;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'promotion';
  is_active: boolean;
}

const TypewriterAnnouncement: React.FC = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  useEffect(() => {
    loadAnnouncements();
  }, []);

  const loadAnnouncements = async () => {
    try {
      const response = await announcementsAPI.getAnnouncements();
      const activeAnnouncements = response.data.filter((ann: Announcement) => ann.is_active);
      setAnnouncements(activeAnnouncements);
    } catch (error) {
      console.error('載入公告失敗:', error);
      // 使用模擬數據作為後備
      setAnnouncements([
        {
          id: 1,
          title: '歡迎光臨',
          content: '歡迎來到我們的商店，享受優質的購物體驗！',
          type: 'info',
          is_active: true
        }
      ]);
    }
  };

  useEffect(() => {
    if (announcements.length === 0) return;

    const currentAnnouncement = announcements[currentIndex];
    const fullText = `${currentAnnouncement.title} - ${currentAnnouncement.content}`;
    
    setIsTyping(true);
    setDisplayText('');
    
    let charIndex = 0;
    const typeInterval = setInterval(() => {
      if (charIndex < fullText.length) {
        setDisplayText(fullText.slice(0, charIndex + 1));
        charIndex++;
      } else {
        clearInterval(typeInterval);
        setIsTyping(false);
        
        // 顯示完成後等待3秒，然後切換到下一個公告
        setTimeout(() => {
          setCurrentIndex((prev) => (prev + 1) % announcements.length);
        }, 3000);
      }
    }, 100); // 打字速度：每100ms一個字符

    return () => clearInterval(typeInterval);
  }, [announcements, currentIndex]);

  if (announcements.length === 0) {
    return null;
  }

  const currentAnnouncement = announcements[currentIndex];
  
  const getBackgroundColor = (type: string) => {
    switch (type) {
      case 'warning':
        return 'bg-red-100 border-red-300 text-red-800';
      case 'promotion':
        return 'bg-green-100 border-green-300 text-green-800';
      default:
        return 'bg-blue-100 border-blue-300 text-blue-800';
    }
  };

  return (
    <div className={`border-2 rounded-lg p-4 mb-6 ${getBackgroundColor(currentAnnouncement.type)}`}>
      <div className="flex items-center justify-center">
        <div className="text-center">
          <span className="text-lg font-medium">
            {displayText}
            {isTyping && (
              <span className="animate-pulse ml-1 text-xl">|</span>
            )}
          </span>
        </div>
      </div>
      
      {/* 公告指示器 */}
      {announcements.length > 1 && (
        <div className="flex justify-center mt-3 space-x-2">
          {announcements.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'bg-current' 
                  : 'bg-current opacity-30'
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default TypewriterAnnouncement;
