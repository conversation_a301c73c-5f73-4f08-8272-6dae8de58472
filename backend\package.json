{"name": "vape-store-backend", "version": "1.0.0", "description": "電子煙線上商店後端API", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "init-db": "node src/scripts/init-database.js", "seed-data": "node src/scripts/seed-data.js"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["vape", "ecommerce", "api"], "author": "MiniMax Agent", "license": "MIT"}