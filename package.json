{"name": "react_repo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "tsc -b && vite build && npm run copy-sitemap", "build:backend": "cd backend && npm install --production", "copy-sitemap": "cp public/sitemap.xml dist/sitemap.xml", "start": "cd backend && npm start", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@types/uuid": "^10.0.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.1", "exceljs": "^4.4.0", "input-otp": "^1.4.1", "lucide-react": "^0.468.0", "next-themes": "^0.4.4", "react": "^18.3.1", "react-day-picker": "^9.4.2", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.29.0", "react-simple-captcha": "^9.3.1", "recharts": "^2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "vaul": "^1.1.1", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.0", "rollup": "^4.28.1", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.3"}, "optionalDependencies": {"@rollup/rollup-darwin-arm64": "^4.28.1", "@rollup/rollup-darwin-x64": "^4.28.1", "@rollup/rollup-linux-arm64-gnu": "^4.28.1", "@rollup/rollup-linux-x64-gnu": "^4.28.1", "@rollup/rollup-win32-x64-msvc": "^4.28.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}