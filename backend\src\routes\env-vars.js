const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const router = express.Router();

// 環境變量文件路徑
const FRONTEND_ENV_PATH = path.join(__dirname, '../../../.env');
const BACKEND_ENV_PATH = path.join(__dirname, '../.env');

// 解析 .env 文件
const parseEnvFile = (content) => {
  const lines = content.split('\n');
  const vars = {};
  let currentSection = '';
  
  lines.forEach(line => {
    const trimmed = line.trim();
    
    // 跳過空行和註釋
    if (!trimmed || trimmed.startsWith('#')) {
      if (trimmed.startsWith('#') && !trimmed.includes('=')) {
        currentSection = trimmed.replace('#', '').trim();
      }
      return;
    }
    
    // 解析變量
    const equalIndex = trimmed.indexOf('=');
    if (equalIndex > 0) {
      const key = trimmed.substring(0, equalIndex).trim();
      const value = trimmed.substring(equalIndex + 1).trim();
      vars[key] = {
        value: value,
        section: currentSection
      };
    }
  });
  
  return vars;
};

// 生成 .env 文件內容
const generateEnvContent = (vars, sections) => {
  let content = '';
  
  sections.forEach(section => {
    if (section.title) {
      content += `\n# ${section.title}\n`;
    }
    
    section.vars.forEach(varKey => {
      if (vars[varKey]) {
        content += `${varKey}=${vars[varKey].value}\n`;
      }
    });
  });
  
  return content;
};

// 獲取所有環境變量
router.get('/frontend', async (req, res) => {
  try {
    const content = await fs.readFile(FRONTEND_ENV_PATH, 'utf8');
    const vars = parseEnvFile(content);
    
    // 按分類組織變量
    const sections = [
      {
        title: '網站基本配置',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_SITE_') || 
          key.startsWith('VITE_BRAND_')
        )
      },
      {
        title: '聯繫信息',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_CONTACT_') || 
          key.startsWith('VITE_SOCIAL_')
        )
      },
      {
        title: '圖片配置',
        vars: Object.keys(vars).filter(key => 
          key.includes('IMAGE')
        )
      },
      {
        title: '產品分類配置',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_CATEGORY_')
        )
      },
      {
        title: '服務特色',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_FEATURE_')
        )
      },
      {
        title: '界面文字 - 導航和按鈕',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_UI_') && 
          !key.startsWith('VITE_UI_FORM_') &&
          !key.startsWith('VITE_UI_VALIDATION_') &&
          !key.startsWith('VITE_UI_NOTIFICATION_') &&
          !key.includes('_MESSAGE') &&
          !key.includes('_ORDER_') &&
          !key.includes('_PRODUCT_')
        )
      },
      {
        title: '界面文字 - 表單標籤',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_UI_FORM_')
        )
      },
      {
        title: '界面文字 - 訊息和狀態',
        vars: Object.keys(vars).filter(key => 
          key.includes('_MESSAGE') || 
          key.startsWith('VITE_UI_SUCCESS_') ||
          key.startsWith('VITE_UI_ERROR_') ||
          key.startsWith('VITE_UI_LOADING') ||
          key.includes('_STOCK')
        )
      },
      {
        title: '界面文字 - 產品相關',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_UI_PRODUCT_')
        )
      },
      {
        title: '界面文字 - 訂單相關',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_UI_ORDER_')
        )
      },
      {
        title: '界面文字 - 通知和提示',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_UI_NOTIFICATION_') ||
          key.startsWith('VITE_UI_CONFIRM_')
        )
      },
      {
        title: '界面文字 - 驗證訊息',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_UI_VALIDATION_')
        )
      },
      {
        title: '版權信息',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_COPYRIGHT_')
        )
      },
      {
        title: '年齡驗證',
        vars: Object.keys(vars).filter(key => 
          key.startsWith('VITE_AGE_')
        )
      }
    ];
    
    res.json({
      success: true,
      vars,
      sections
    });
  } catch (error) {
    console.error('讀取前端環境變量失敗:', error);
    res.status(500).json({ error: '讀取環境變量失敗' });
  }
});

// 獲取後端環境變量
router.get('/backend', async (req, res) => {
  try {
    const content = await fs.readFile(BACKEND_ENV_PATH, 'utf8');
    const vars = parseEnvFile(content);
    
    res.json({
      success: true,
      vars
    });
  } catch (error) {
    console.error('讀取後端環境變量失敗:', error);
    res.status(500).json({ error: '讀取環境變量失敗' });
  }
});

// 更新前端環境變量
router.put('/frontend', async (req, res) => {
  try {
    const { vars, sections } = req.body;
    
    // 生成新的 .env 內容
    const content = generateEnvContent(vars, sections);
    
    // 寫入文件
    await fs.writeFile(FRONTEND_ENV_PATH, content, 'utf8');
    
    res.json({
      success: true,
      message: '前端環境變量已更新'
    });
  } catch (error) {
    console.error('更新前端環境變量失敗:', error);
    res.status(500).json({ error: '更新環境變量失敗' });
  }
});

// 更新後端環境變量
router.put('/backend', async (req, res) => {
  try {
    const { vars } = req.body;
    
    // 生成 .env 內容
    let content = '';
    Object.entries(vars).forEach(([key, data]) => {
      content += `${key}=${data.value}\n`;
    });
    
    // 寫入文件
    await fs.writeFile(BACKEND_ENV_PATH, content, 'utf8');
    
    res.json({
      success: true,
      message: '後端環境變量已更新'
    });
  } catch (error) {
    console.error('更新後端環境變量失敗:', error);
    res.status(500).json({ error: '更新環境變量失敗' });
  }
});

// 重置為默認值
router.post('/reset', async (req, res) => {
  try {
    const { type } = req.body; // 'frontend' or 'backend'
    
    if (type === 'frontend') {
      // 讀取 .env.example 作為默認值
      const examplePath = path.join(__dirname, '../../../.env.example');
      const exampleContent = await fs.readFile(examplePath, 'utf8');
      await fs.writeFile(FRONTEND_ENV_PATH, exampleContent, 'utf8');
    } else if (type === 'backend') {
      const examplePath = path.join(__dirname, '../env.example');
      const exampleContent = await fs.readFile(examplePath, 'utf8');
      await fs.writeFile(BACKEND_ENV_PATH, exampleContent, 'utf8');
    }
    
    res.json({
      success: true,
      message: '環境變量已重置為默認值'
    });
  } catch (error) {
    console.error('重置環境變量失敗:', error);
    res.status(500).json({ error: '重置環境變量失敗' });
  }
});

module.exports = router;
